<template>
  <div class="">
    <div
      class="nuxt-link whitespace-nowrap flex justify-between px-4 py-1.5 items-center cursor-pointer"
      :class="item.selected ? 'bg-[#DEDEDE]' : ''"
      @click="
        [
          store.commit('home/SET_SELECTED_EMAIL_LABEL', {
            id: item.id,
            emailLabels,
            item: item,
          }),
          emit('select-single-item', 'singleLabel'),
        ]
      "
    >
      <div
        class="menu-container w-full flex !space-x-[18px] items-center justify-between"
      >
        <div class="flex !space-x-[18px] items-center flex-grow">
          <SharedIconHubEmailsSidebarLabelsProduct />
          <!-- <component v-if="item.image" :is="item.image"></component> -->
          <p class="text-[#434343] text-xl">
            {{
              item.name.charAt(0).toUpperCase() +
              item.name.slice(1).toLowerCase()
            }}
          </p>
        </div>
        <ClientOnly v-if="item.children && item.children.length > 0">
          <fa
            class="text-xl transition-all duration-300 ease-in-out text-[#525252] cursor-pointer"
            :class="[item.toggle ? 'rotate-180' : '']"
            :icon="['fas', 'caret-down']"
            @click.stop="
              store.commit(
                'home/SET_TOGGLE_TO_HIDE_SHOW_EMAIL_CHILD_LABELS',
                item.id,
              )
            "
          />
        </ClientOnly>
      </div>
      <!-- <p v-if="item.unread > 0" class="text-sm">
        {{ item.unread }}
      </p> -->
    </div>

    <!-- Recursively render children -->
    <div
      v-if="item.children && item.children.length > 0 && item.toggle"
      class="ml-4 mt-2"
    >
      <HomeRealtimeFeedRssEmailLabelItem
        v-for="child in item.children"
        :key="child.id"
        :item="child"
      />
    </div>
  </div>
</template>

<script setup>
import { useStore } from 'vuex'
const emit = defineEmits(['select-single-item'])
const props = defineProps({
  item: Object,
})
const store = useStore()
const route = useRoute()
const emailLabels = computed(() => store.state.home.emailLabels)
</script>

<style lang="scss" scoped>
.router-link-exact-active,
.router-link-active {
  @apply bg-blue-200 text-white;
}
</style>
