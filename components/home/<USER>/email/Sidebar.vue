<script setup lang="ts">
import { useStore } from 'vuex'

const emit = defineEmits(['select-single-item'])
const store = useStore()
const route = useRoute()

const emailLabels = computed(() => store.state.home.emailLabels)
</script>

<template>
  <div class="flex flex-col">
    <!-- <div class="flex flex-col">
      <template v-for="menuItem in emailLabels">
        <div
          v-if="menuItem.selected"
          :key="menuItem.id"
          class="nuxt-link cursor-pointer whitespace-nowrap flex justify-between px-4 py-2 items-center rounded-full"
          @click=""
        >
          <div class="menu-container flex !space-x-[18px] items-center">
            <component v-if="menuItem.image" :is="menuItem.image"></component>
            <p>{{ menuItem.name }}</p>
          </div>
          <p v-if="menuItem.unread > 0" class="text-sm">
            {{ menuItem.unread }}
          </p>
        </div>
      </template>
    </div> -->
    <div class="h-full">
      <div
        v-if="emailLabels && emailLabels.length > 0"
        class="flex flex-col mt-2"
      >
        <HomeRealtimeFeedRssEmailLabelItem
          v-for="menuItem in emailLabels"
          :key="menuItem.id"
          :item="menuItem"
          @select-single-item="($event) => emit('select-single-item', $event)"
        />
      </div>
      <div v-else class="flex justify-center items-center h-full w-full">
        <p class="text-2xl text-center text-black font-semibold">
          No Labels Found
        </p>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.router-link-exact-active,
.router-link-active {
  @apply bg-blue-200 text-white;
}
</style>
