<script setup lang="ts">
const props = defineProps({
  color: {
    type: String,
    default: '#505050',
  },
})
</script>

<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    height="12"
    viewBox="0 0 12 12"
  >
    <path
      id="Path_2198"
      data-name="Path 2198"
      d="M223-735.722l-4.471,4.471a.865.865,0,0,1-.639.251.865.865,0,0,1-.639-.251.865.865,0,0,1-.251-.639.865.865,0,0,1,.251-.639L221.722-737l-4.471-4.471a.865.865,0,0,1-.251-.639.865.865,0,0,1,.251-.639.865.865,0,0,1,.639-.251.865.865,0,0,1,.639.251L223-738.278l4.471-4.471a.865.865,0,0,1,.639-.251.865.865,0,0,1,.639.251.865.865,0,0,1,.251.639.865.865,0,0,1-.251.639L224.278-737l4.471,4.471a.865.865,0,0,1,.251.639.865.865,0,0,1-.251.639.865.865,0,0,1-.639.251.865.865,0,0,1-.639-.251Z"
      transform="translate(-217 743)"
      :fill="color"
    />
  </svg>
</template>
