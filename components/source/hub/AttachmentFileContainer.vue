<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()
interface Props {
  formatSize?: Function
  removeFile?: Function | string
  attachedFiles?: Array<File>
  fileUrls?: Array<string>
  uploadProgress?: Array<number>
}

const props = withDefaults(defineProps<Props>(), {
  formatSize: () => {},
  removeFile: () => {},
  attachedFiles: () => [],
  fileUrls: () => [],
  uploadProgress: () => [],
})
const uploadProgress = ref<number[]>([0])
const { formatSize } = FileUpload()
const simulateUploadProgress = (index: number) => {
  console.log(uploadProgress.value[index], 'simulateUploadProgress')
  const interval = setInterval(() => {
    if (uploadProgress.value[index] >= 100) {
      clearInterval(interval)
      uploadProgress.value.push(0)
    } else {
      console.log('simulateUploadProgress', uploadProgress.value[index])
      uploadProgress.value[index] += 10
    }
  }, 500)
}
// Watch the attachedFiles array
watch(
  props.attachedFiles,
  (newFiles, oldFiles) => {
    console.log('newFiles', newFiles, oldFiles)

    if (newFiles.length > oldFiles.length) {
      const addedCount = newFiles.length - oldFiles.length
      for (let i = 0; i < addedCount; i++) {
        uploadProgress.value.push(0)
      }
    } else if (newFiles.length < oldFiles.length) {
      uploadProgress.value.splice(newFiles.length)
    }
  },
  { deep: true },
)
</script>

<template>
  <!-- File Preview Below Editor -->
  <div class="space-y-2 mb-2">
    <div
      v-for="(file, index) in attachedFiles"
      :key="index"
      tabindex="0"
      class="flex items-center justify-between"
      :class="
        fileUrls && fileUrls.length > 0
          ? 'attachment-box'
          : 'p-3 py-0 h-[53px] bg-[#F1F2F6] !max-w-[244px] attachment-drive-box'
      "
    >
      <div class="flex items-center space-x-2 flex-grow">
        <a
          v-if="fileUrls && fileUrls.length > 0"
          :href="fileUrls[index]"
          :download="file.name"
          class="text-sm font-bold text-[#15c] hover:underline attachment-text"
          :class="
            uploadProgress[index] < 100 ? 'max-w-[232px]' : 'max-w-[315px]'
          "
        >
          {{ file.name }}
        </a>
        <div v-else class="flex space-x-4 items-center">
          <img
            class="size-5"
            src="/social/email/image-color-icon.png"
            alt="image icon"
          />
          <p
            class="text-sm flex-grow font-normal text-[#525252] line-clamp-1 attachment-link-text cursor-pointer"
          >
            {{ file.name || file.file.name }}
          </p>
        </div>
        <p
          v-if="fileUrls && fileUrls.length > 0"
          class="text-sm font-bold text-[#444746] py-[3px] whitespace-nowrap"
        >
          ({{ formatSize(file.size) }})
        </p>
      </div>
      <div class="flex items-center space-x-2">
        <!-- Progress Bar  -->
        <div
          v-if="fileUrls && fileUrls.length > 0 && uploadProgress[index] < 100"
          class="w-[100px] h-3 bg-white border border-[#999] overflow-hidden"
        >
          <div
            class="h-full bg-blue-500 transition-all duration-300"
            :style="{
              width: uploadProgress[index] + '%',
            }"
          >
            <p class="hidden">{{ simulateUploadProgress(index) }}</p>
          </div>
        </div>
        <button
          @click="
            fileUrls && fileUrls.length > 0
              ? removeFile === 'removeDriveAttachment'
                ? store.commit('emails/REMOVE_ATTACHMENT_FROM_DRIVE', index)
                : removeFile === 'removePhotoAttachment'
                  ? store.commit('emails/REMOVE_ATTACHMENT_FROM_PHOTO', index)
                  : removeFile(index)
              : removeFile === 'removeDriveLink'
                ? store.commit('emails/REMOVE_SPECIFIC_LINK_FILE', file.id)
                : removeFile === 'removeComposeDriveLink'
                  ? store.commit(
                      'emails/REMOVE_SPECIFIC_LINK_FILE_FROM_SPECIFIC_COMPOSE',
                      file.id,
                    )
                  : store.commit(
                      'emails/REMOVE_UPLOAD_FILE_FROM_DRIVE',
                      file.id,
                    )
          "
          class="text-red-500 hover:text-red-700 text-sm close-button mt-1.5"
          :class="fileUrls && fileUrls.length > 0 ? 'block' : 'hidden'"
          title="Remove"
        >
          <ClientOnly>
            <fa
              class="text-[#222] text-xl font-bold"
              :icon="['fas', 'times']"
            />
          </ClientOnly>
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.attachment-box {
  background-color: #f5f5f5;
  border: 1px solid transparent;
  font-weight: bold;
  margin: 0 0 9px;
  overflow-y: hidden;
  padding: 4px 4px 4px 8px;
  max-width: 589px;
}
.attachment-text {
  display: inline-block;
  overflow: hidden;
  padding: 3px 0;
  text-overflow: ellipsis;
  vertical-align: bottom;
  white-space: nowrap;
  // max-width: 315px;
}
.attachment-box:focus {
  background-color: rgba(32, 33, 36, 0.12);
  box-shadow: inset 0 0 0 1px rgb(189, 193, 198);
}
.attachment-drive-box:hover .close-button {
  display: block;
}
.attachment-drive-box:hover .attachment-link-text {
  text-decoration: underline;
}
</style>
