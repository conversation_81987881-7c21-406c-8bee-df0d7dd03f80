<script setup lang="ts">
import { useStore } from 'vuex'
import Spelling<PERSON>heck from './SpellingCheck.vue'

const store = useStore()

const composeArray = computed(() => store.state.emails.composeArray)
const deleteSpecificComposeItem = (id: number) => {
  store.commit('emails/DELETE_A_SPECIFIC_COMPOSE_MESSAGE', id)
}
onMounted(() => {
  store.commit('emails/RESET_UPLOAD_FILE_FROM_DRIVE')
  store.commit('emails/RESET_DRIVE_LINK_FILES')
})
</script>

<template>
  <div class="w-full h-full overflow-hidden relative">
    <div
      v-if="composeArray.length > 1"
      class="w-full flex space-x-2 bg-[#F1F2F6] px-2 pt-2 overflow-x-auto custom-scroll"
    >
      <div
        v-for="(item, index) in composeArray"
        :key="item.id"
        class="cursor-pointer rounded-tl rounded-tr"
        :class="
          item.active
            ? 'bg-[#FFFFFF]'
            : 'bg-[#F1F2F6] border-[0.2px] border-[#525252] border-opacity-20'
        "
        @click="store.commit('emails/SET_ACTIVE_COMPOSE_MESSAGE', item.id)"
      >
        <div
          class="px-4 py-2 pb-1.5 flex items-center justify-between space-x-5"
        >
          <p class="text-[#525252] font-semibold whitespace-nowrap">
            New Message ({{ index + 1 }})
          </p>
          <SharedIconHubEmailsCrossIcon
            @click.stop="deleteSpecificComposeItem(item.id)"
          />
        </div>
      </div>
    </div>
    <div
      class="w-full bg-white custom-scroll"
      :class="
        composeArray.length > 1 ? 'h-[calc(100%-58px)]' : 'h-[calc(100%-10px)]'
      "
    >
      <template v-for="item in composeArray" :key="item.id">
        <div
          v-show="item.active"
          class="w-full h-full px-4 py-2 flex items-center justify-between"
        >
          <SourceHubEmailsReplyBox
            class="w-full h-full"
            :deleteSpecificComposeItem="deleteSpecificComposeItem"
            :itemId="item.id"
            :fileUploadEvents="item.forwardMessageEvents"
            :attachmentFromDrive="item.attachmentFromDrive"
            :uploadedComposeFiles="item.uploadedFiles"
            :linkFiles="item.linkFiles"
            :recipientInfo="item.recipientInfo"
            :insertPhotoFile="item.insertPhotoFile"
            :attachmentFromPhoto="item.attachmentFromPhoto"
          />
        </div>
        <SpellingCheck v-if="item?.active && item?.spellingCheck" />
      </template>
    </div>
  </div>
</template>
