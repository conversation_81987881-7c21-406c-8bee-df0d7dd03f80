<script setup lang="ts">
import { subDays } from 'date-fns'
import { useStore } from 'vuex'
import type { InsertFiles } from '~/types/gmail'
import FilesByGrouped from './google-drive/FilesByGrouped.vue'

type SelectedButton = 'Recent' | 'My Drive' | 'Shared with me' | 'Upload'

const store = useStore()

const emit = defineEmits(['add-attachment'])

const composeArray = computed(() => store.state.emails.composeArray)
interface ButtonOptions {
  id: number
  text: string
  select: boolean
}
const buttonOptions = ref<ButtonOptions[]>([
  {
    id: 1,
    text: 'Recent',
    select: true,
  },
  {
    id: 2,
    text: 'My Drive',
    select: false,
  },
  {
    id: 3,
    text: 'Shared with me',
    select: false,
  },
  {
    id: 4,
    text: 'Upload',
    select: false,
  },
])
const selectedButton = ref<SelectedButton>('Recent')
const setButtonOptionsSelect = (id: number) => {
  buttonOptions.value.forEach((buttonOption: ButtonOptions) => {
    if (buttonOption.id === id) {
      buttonOption.select = true
      selectedButton.value = buttonOption.text as SelectedButton
    } else {
      buttonOption.select = false
    }
  })
}

const today = new Date()
const formatDate = (date: Date) => date.toISOString()

const insertFiles = ref<InsertFiles[]>([
  {
    id: 1,
    img: '/social/email/1.png',
    type: 'image/png',
    title: 'Minecraft-1',
    name: 'Minecraft-1.png',
    date: formatDate(today),
    select: false,
    size: 300,
    owner: 'me',
  },
  {
    id: 2,
    img: '/social/email/2.png',
    type: 'image/png',
    title: 'Minecraft-thumbnail',
    name: 'Minecraft-thumbnail.png',
    date: formatDate(subDays(today, 2)),
    select: false,
    size: 350,
    owner: 'me',
  },
  {
    id: 3,
    img: '/social/email/3.png',
    type: 'image/png',
    title: 'Minecraft-thumbnail2',
    name: 'Minecraft-thumbnail2.png',
    date: formatDate(subDays(today, 2)),
    select: false,
    size: 310,
    owner: 'me',
  },
  {
    id: 4,
    img: '/social/email/4.png',
    type: 'image/png',
    title: 'Minecraft-2',
    name: 'Minecraft-2.png',
    date: formatDate(subDays(today, 3)),
    select: false,
    size: 320,
    owner: 'me',
  },
  {
    id: 5,
    img: '/social/email/5.png',
    type: 'image/png',
    title: 'Minecraft-thumbnail3',
    name: 'Minecraft-thumbnail3.png',
    date: formatDate(subDays(today, 4)),
    select: false,
    size: 400,
    owner: 'me',
  },
  {
    id: 6,
    img: '/social/email/1.png',
    type: 'image/png',
    title: 'Minecraft-thumbnail4',
    name: 'Minecraft-thumbnail4.png',
    date: formatDate(subDays(today, 5)),
    select: false,
    size: 410,
    owner: 'me',
  },
  {
    id: 7,
    img: '/social/email/2.png',
    type: 'image/png',
    title: 'Minecraft-thumbnail5',
    name: 'Minecraft-thumbnail5.png',
    date: formatDate(subDays(today, 6)),
    select: false,
    size: 450,
    owner: 'me',
  },
  {
    id: 8,
    img: '/social/email/3.png',
    type: 'image/png',
    title: 'Minecraft-thumbnail6',
    name: 'Minecraft-thumbnail6.png',
    date: formatDate(subDays(today, 7)),
    select: false,
    size: 420,
    owner: 'me',
  },
  {
    id: 9,
    img: '/social/email/4.png',
    type: 'image/png',
    title: 'Minecraft-thumbnail7',
    name: 'Minecraft-thumbnail7.png',
    date: formatDate(subDays(today, 8)),
    select: false,
    size: 500,
    owner: 'me',
  },
  {
    id: 10,
    img: '',
    type: 'folder',
    title: 'Camera',
    name: '',
    date: formatDate(subDays(today, 35)),
    select: false,
    size: null,
    owner: 'me',
  },
  {
    id: 11,
    img: '',
    type: 'folder',
    title: 'Product',
    name: '',
    date: formatDate(subDays(today, 36)),
    select: false,
    size: null,
    owner: 'me',
  },
  {
    id: 12,
    img: '',
    type: 'folder',
    title: 'Picture',
    name: '',
    date: formatDate(subDays(today, 37)),
    select: false,
    size: null,
    owner: 'me',
  },
  {
    id: 13,
    img: '',
    type: 'folder',
    title: 'Video',
    name: '',
    date: formatDate(subDays(today, 38)),
    select: false,
    size: null,
    owner: 'me',
  },
  {
    id: 14,
    img: '',
    type: 'folder',
    title: 'Documents',
    name: '',
    date: formatDate(subDays(today, 39)),
    select: false,
    size: null,
    owner: 'me',
  },
  {
    id: 15,
    img: '',
    type: 'folder',
    title: 'Personal',
    name: '',
    date: formatDate(subDays(today, 40)),
    select: false,
    size: null,
    owner: 'me',
  },
  {
    id: 16,
    img: '/social/email/docs.png',
    type: 'docs',
    title: 'New docs',
    name: 'New docs.docx',
    date: formatDate(subDays(today, 45)),
    select: false,
    size: 200,
    owner: 'me',
  },
  {
    id: 17,
    img: '/social/email/docs.png',
    type: 'docs',
    title: 'New docs 3',
    name: 'New docs 3.docx',
    date: formatDate(subDays(today, 50)),
    select: false,
    size: 200,
    owner: 'me',
  },
  {
    id: 18,
    img: '/social/email/pdf.png',
    type: 'application/pdf',
    title: 'New pdf 4',
    name: 'New pdf 4.pdf',
    date: formatDate(subDays(today, 55)),
    select: false,
    size: 400,
    owner: 'me',
  },
  {
    id: 19,
    img: '/social/email/pdf.png',
    type: 'application/pdf',
    title: 'New pdf 5',
    name: 'New pdf 5.pdf',
    date: formatDate(subDays(today, 60)),
    select: false,
    size: 400,
    owner: 'me',
  },
  {
    id: 20,
    img: '/social/email/pdf.png',
    type: 'application/pdf',
    title: 'New pdf 6',
    name: 'New pdf 6.pdf',
    date: formatDate(subDays(today, 65)),
    select: false,
    size: 400,
    owner: 'me',
  },
])

const filteredFiles = ref<InsertFiles[]>(insertFiles.value)

const itemSelect = ref<boolean>(false)
const isAscSorting = ref<boolean>(true)

const selectedFiles = computed(() => {
  return insertFiles.value.filter((file: InsertFiles) => file.select)
})

const selctedDriveFiles = ref<any>([])
const selectedInsertFile = (files: InsertFiles[], id: number) => {
  files.forEach((file: InsertFiles, index) => {
    if (file.id === id) {
      file.select = !file.select
      if (file.select) {
        convertToFile(file).then((item) => {
          selctedDriveFiles.value.push(item)
        })
      } else {
        const removeIndex = selctedDriveFiles.value.findIndex(
          (f: any) => f.name === file.name && f.type === file.type,
        )
        if (removeIndex !== -1) {
          selctedDriveFiles.value.splice(removeIndex, 1)
        }
      }
    }
  })
  console.log(selctedDriveFiles.value, 'selctedDriveFiles.value')
  itemSelect.value = insertFiles.value.some((file: InsertFiles) => file.select)
}
// Convert it into a File
async function convertToFile(obj: any): Promise<File> {
  // fetch the file (works if `img` is a URL or relative path in /public folder)
  const response = await fetch(obj.img)
  const blob = await response.blob()

  // return File
  return new File([blob], obj.name, { type: obj.type })
}
const resetAllSeletedFiles = () => {
  insertFiles.value.forEach((insertFile: InsertFiles) => {
    insertFile.select = false
  })
  itemSelect.value = false
  selctedDriveFiles.value = []
}
const toggleListGrid = ref<boolean>(false)
const MAX_FILE_SIZE_MB = 25
const isDragging = ref(false)
const fileInput = ref<HTMLInputElement | null>(null)
const { $toast } = useNuxtApp()
const onDrop = (event: DragEvent) => {
  isDragging.value = false
  const files = event.dataTransfer?.files
  if (files) insertMultipleImages(Array.from(files))
}

const onDragOver = () => {
  isDragging.value = true
}
const onDragLeave = () => {
  isDragging.value = false
}
// Trigger hidden file input
const triggerFileInput = () => {
  fileInput.value?.click()
}

// File validation
const validateFile = (file: File): string | null => {
  if (!file.type.startsWith('image/')) return 'Only image files are allowed.'
  if (file.size > MAX_FILE_SIZE_MB * 1024 * 1024)
    return 'Image must be 25MB or less.'
  return null
}

const totalImages = ref(0)
const completedImages = ref(0)

const insertMultipleImages = async (files: File[]) => {
  totalImages.value = 0
  const validFiles = files.filter((file) => !validateFile(file))
  totalImages.value = validFiles.length
  completedImages.value = 0
  for (const file of files) {
    const error = validateFile(file)
    if (error) {
      alert(error)
      continue
    }
    const base64 = await readFileAsBase64(file)
    completedImages.value++
    store.commit('emails/SET_UPLOAD_FILE_FROM_DRIVE', file)
  }
  // Optional: Reset after short delay
  setTimeout(() => {
    totalImages.value = 0
    completedImages.value = 0
    store.commit('emails/SET_DRIVE_INSERT_FILE_MODAL', false)
  }, 2000)
}
const progress = computed(() => {
  return totalImages.value === 0
    ? 0
    : Math.round((completedImages.value / totalImages.value) * 100)
})
// Utility to read file as base64 using Promise
const readFileAsBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(reader.result as string)
    reader.onerror = reject
    reader.readAsDataURL(file)
  })
}

const onFileChange = (event: Event) => {
  const input = event.target as HTMLInputElement
  handleFiles(input.files)
}

const handleFiles = (fileList: FileList | null) => {
  if (!fileList) return
  insertMultipleImages(Array.from(fileList))
  // for (let i = 0; i < fileList.length; i++) {
  // insertImage(fileList[i])
  // }
}
const handleAddLinkAction = () => {
  store.commit('emails/SET_DRIVE_INSERT_FILE_MODAL', false),
    composeArray.value && composeArray.value.length > 0
      ? store.commit(
          'emails/SET_DRIVE_LINK_COMPOSE_SECTION',
          selctedDriveFiles.value,
        )
      : store.commit('emails/SET_DRIVE_LINK_FILES', selctedDriveFiles.value)
}

const handleSelectedFilter = (filter: {
  id: number
  text: string
  value: string
}) => {
  const order = isAscSorting.value ? 1 : -1
  let sorted: InsertFiles[] = []

  if (filter.value === 'name') {
    // Fixed: Compare name with name (or title with title)
    sorted = [...insertFiles.value].sort((a, b) => {
      // Use title for comparison since that's what's displayed, or use name if preferred
      const nameA = a.title || a.name || ''
      const nameB = b.title || b.name || ''
      return nameA.localeCompare(nameB) * order
    })
  } else {
    sorted = [...insertFiles.value].sort((a, b) => {
      const dateA = new Date(a.date)
      const dateB = new Date(b.date)

      if (isNaN(dateA.getTime()) || isNaN(dateB.getTime())) {
        return 0
      }

      return (dateA.getTime() - dateB.getTime()) * order
    })
  }

  filteredFiles.value = sorted
}
</script>

<template>
  <div
    class="overflow-hidden max-w-[1280px] w-[90%] max-h-[622px] h-full rounded-2xl bg-white fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-[999999999]"
  >
    <div class="max-h-[622px] h-full rounded-2xl bg-white overflow-hidden">
      <div @click="resetAllSeletedFiles">
        <div class="grid grid-cols-3 gap-1 items-center px-6 py-4">
          <div class="text-left flex !space-x-2">
            <SharedIconHubEmailsDriveColorIcon />
            <p class="text-[#525252] text-lg font-semibold whitespace-nowrap">
              Insert file using Google Drive
            </p>
          </div>
          <div
            class="flex items-center justify-between space-x-3 bg-[#F1F2F6] px-[26px] rounded-full max-w-[480px]"
          >
            <div class="flex-grow">
              <div class="flex space-x-2 items-center">
                <fa class="text-[#707070]" :icon="['fas', 'search']" />
                <input
                  class="text-[#707070] bg-[#F1F2F6] flex-grow border-none outline-none py-1.5"
                  type="text"
                  placeholder="Search in Drive or paste URL"
                />
              </div>
            </div>
            <SharedIconHubEmailsFilterIcon />
          </div>
          <SharedIconHubEmailsCrossIcon
            class="cursor-pointer ml-auto"
            @click.stop="
              store.commit('emails/SET_DRIVE_INSERT_FILE_MODAL', false)
            "
          />
        </div>
        <div class="px-6 py-4 border-b border-[#f1f1f2]">
          <div class="flex space-x-4 w-full">
            <button
              v-for="buttonOption in buttonOptions"
              :key="buttonOption.id"
              class="px-6 w-fit h-[35px] rounded-full font-semibold flex justify-center items-center"
              :class="
                buttonOption.select
                  ? 'text-white bg-[#4A71D4]'
                  : 'bg-[#f1f2f6] text-[#525252]'
              "
              @click="setButtonOptionsSelect(buttonOption.id)"
            >
              {{ buttonOption.text }}
            </button>
          </div>
        </div>
        <div
          v-if="selectedButton !== 'Upload'"
          class="px-6 py-4 border-b border-[#f1f1f2] flex justify-between items-center"
        >
          <p class="text-[#707070]">{{ selectedButton }}</p>
          <SharedIconHubEmailsListIcon
            v-if="!toggleListGrid"
            @click="toggleListGrid = true"
            class="cursor-pointer"
          />
          <SharedIconHubEmailsGridIcon
            v-else
            @click="toggleListGrid = false"
            class="cursor-pointer"
          />
        </div>
      </div>

      <FilesByGrouped
        v-if="
          selectedButton === 'Recent' ||
          selectedButton === 'My Drive' ||
          selectedButton === 'Shared with me'
        "
        v-model="isAscSorting"
        @selectedFilter="handleSelectedFilter"
        :selectedButton="selectedButton"
        :toggleListGrid="toggleListGrid"
        :insertFiles="filteredFiles"
        @resetAllSelectedFiles="resetAllSeletedFiles"
        @selectedInsertFile="selectedInsertFile"
      />
      <div
        v-else
        class="flex flex-col w-full h-[calc(100%-201px)] justify-center items-center"
        @dragover.prevent="onDragOver"
        @dragleave="onDragLeave"
        @drop.prevent="onDrop"
      >
        <div
          class="flex flex-col w-full h-full justify-center items-center custom-scroll"
        >
          <img
            class="max-h-[160px]"
            src="/images/icon/upload.svg"
            alt="upload_background"
          />
          <!-- v-if="progress > 0 && progress < 100" -->
          <div
            v-if="totalImages !== 0"
            class="w-[80%] bg-gray-200 rounded h-2 mt-2 mx-auto"
          >
            <div
              class="bg-blue-500 h-full transition-all duration-300"
              :style="{ width: `${progress}%` }"
            ></div>
          </div>
          <div class="flex flex-col justify-center items-center" v-else>
            <button
              class="mt-8 px-6 text-white bg-[#4A71D4] w-fit h-[35px] rounded-full font-semibold flex justify-center items-center"
              @click="triggerFileInput"
            >
              Browse
            </button>
            <p class="!mt-3.5 text-lg text-[#707070] text-center">
              or drag files here
            </p>
          </div>
          <!-- File input -->
          <input
            ref="fileInput"
            type="file"
            multiple
            accept="image/*"
            class="hidden"
            @change="onFileChange"
          />
        </div>
      </div>
      <Transition name="slide-up">
        <div
          v-if="itemSelect"
          class="w-full py-4 px-6 flex justify-between space-x-2 border-t border-[#f1f1f2] bg-white sticky bottom-0 left-0 z-1"
        >
          <div class="flex space-x-[26px] items-center">
            <SharedIconHubEmailsCrossIcon
              class="cursor-pointer"
              @click.stop="resetAllSeletedFiles"
            />
            <p class="text-[#525252] text-lg">
              {{ selectedFiles.length }} selected
            </p>
          </div>
          <div class="flex space-x-2 items-center">
            <button
              class="w-fit h-[35px] px-6 flex justify-center items-center border border-[#4A71D4] text-[#4A71D4] font-semibold rounded-full"
              @click.stop="
                store.commit('emails/SET_DRIVE_INSERT_FILE_MODAL', false),
                  store.commit(
                    'emails/SET_DRIVE_ATTACHMENT_FILES',
                    selctedDriveFiles,
                  )
              "
            >
              Add as attachment
            </button>
            <button
              class="w-fit h-[35px] px-6 flex justify-center items-center bg-[#4A71D4] text-white font-semibold rounded-full"
              @click.stop="handleAddLinkAction"
            >
              Add as link
            </button>
          </div>
        </div>
      </Transition>
    </div>
  </div>
</template>

<style scoped>
.file-box {
  background: white;
  border: 1px solid #c2c2c2;
  border-radius: 8px;
  opacity: 1;
}
.active-file-box {
  background: #e3efff;
  border: 1px solid #c2c2c2;
  border-radius: 8px;
  opacity: 1;
}
.slide-up-enter-active,
.slide-up-leave-active {
  transition: transform 0.5s ease;
}
.slide-up-enter-from,
.slide-up-leave-to {
  transform: translateY(100%);
}
</style>
