<script setup lang="ts">
import { useStore } from 'vuex'
import { Editor, EditorContent } from '@tiptap/vue-3'

const emit = defineEmits(['fileChange'])

interface Props {
  hideReplyBox?: Function
  deleteSpecificComposeItem?: Function
  showLinkPopup?: Function
  itemId?: number
  editor?: Editor | null
  hideLinkPopup?: Function
}

const props = withDefaults(defineProps<Props>(), {
  hideReplyBox: () => {},
  deleteSpecificComposeItem: (id: number) => {},
  showLinkPopup: () => {},
  itemId: 0,
  editor: null,
  hideLinkPopup: () => {},
})

const store = useStore()
const showTextFormattedMenu = ref(false)
const dialogElem = ref<HTMLElement | null>(null)
const showConfidentialModeModal = computed(
  () => store.state.emails.showConfidentialModeModal,
)
const driveAttachmentFiles = computed(
  () => store.state.emails.driveAttachmentFiles,
)
const photoAttachmentFiles = computed(
  () => store.state.emails.photoAttachmentFiles,
)
const composeArray = computed(() => store.state.emails.composeArray)
const showEmojiPicker = ref<boolean>(false)
const showSignatureMenu = ref<boolean>(false)
const showMoreOptions = ref<boolean>(false)
onMounted(() => {
  document.addEventListener('click', () => {
    // showTextFormattedMenu.value = false
    showEmojiPicker.value = false
    showSignatureMenu.value = false
    showMoreOptions.value = false
  })
  dialogElem.value = document.getElementById('confidentialDialog')
})
onUnmounted(() => {
  document.removeEventListener('click', () => {
    showEmojiPicker.value = false
    showSignatureMenu.value = false
    showMoreOptions.value = false
    // showTextFormattedMenu.value = false
  })
})
const toggleEmojiPicker = () => {
  showEmojiPicker.value = !showEmojiPicker.value
  showSignatureMenu.value = false
  showMoreOptions.value = false
  props.hideLinkPopup()
}
const fileInput = ref<HTMLInputElement>()
const triggerFileInput = () => {
  fileInput.value?.click()
}
const MAX_FILE_SIZE_MB = 25

// const attachedFiles = ref<File[]>([])
// const fileUrls = ref<string[]>([])
// const uploadProgress = ref<number[]>([])
const { $toast } = useNuxtApp()
const {
  handleFileChange,
  removeFile,
  formatSize,
  attachedFiles,
  fileUrls,
  uploadProgress,
  resetFiles,
} = FileUpload()
// // Handle file input
const handleFileChangeLocal = (event: Event) => {
  const input = event.target as HTMLInputElement
  const files = input.files
  handleFileChange(files)
  if (fileInput.value) fileInput.value.value = ''
}

watch(
  () => driveAttachmentFiles.value,
  (value) => {
    console.log(value, 'value')
    if (value && value.length > 0) {
      handleFileChange(value)
      console.log(
        uploadProgress.value[0],
        'uploadProgress',
        composeArray.value.length,
      )
      if (composeArray.value && composeArray.value.length > 0) {
        store.commit('emails/SET_ATTACHMENT_FROM_DRIVE', {
          attachedFiles: attachedFiles.value,
          fileUrls: fileUrls.value,
          formatSize: formatSize,
          // uploadProgress: uploadProgress.value,
          removeFile: 'removeDriveAttachment',
        })
        store.commit('emails/RESET_DRIVE_ATTACHMENT_FILES')
        setTimeout(() => {
          resetFiles()
        })
      }
    }
  },
)
watch(
  () => photoAttachmentFiles.value,
  (value) => {
    console.log(value, 'value')
    if (value && value.length > 0) {
      handleFileChange(value)
      console.log(
        uploadProgress.value[0],
        'uploadProgress',
        composeArray.value.length,
      )
      if (composeArray.value && composeArray.value.length > 0) {
        store.commit('emails/SET_ATTACHMENT_FROM_PHOTO', {
          attachedFiles: attachedFiles.value,
          fileUrls: fileUrls.value,
          formatSize: formatSize,
          // uploadProgress: uploadProgress.value,
          removeFile: 'removePhotoAttachment',
        })
        store.commit('emails/RESET_PHOTO_ATTACHMENT_FILES')
        setTimeout(() => {
          resetFiles()
        })
      }
    }
  },
)
defineExpose({
  attachedFiles,
  removeFile,
  fileUrls,
  formatSize,
  uploadProgress,
})
</script>

<template>
  <div class="flex justify-between items-center relative">
    <div class="flex space-x-4 items-center">
      <button
        class="flex justify-center items-center rounded-full h-[33px] bg-[#4A71D4] text-white text-sm font-semibold"
      >
        <div
          class="flex justify-center items-center px-6 border-r border-white"
        >
          Send
        </div>
        <div class="flex justify-center items-center px-3">
          <SharedIconHubEmailsDownArrow
            class="w-3 h-2 items-center"
            color="white"
          />
        </div>
      </button>
      <div class="flex space-x-3">
        <div
          class="hover:bg-[#F1F2F6] w-7 h-7 flex justify-center items-center rounded-md cursor-pointer"
          :class="showTextFormattedMenu ? 'bg-[#F1F2F6]' : ''"
          @click.stop="showTextFormattedMenu = !showTextFormattedMenu"
        >
          <SharedIconHubEmailsFormattingIcon />
        </div>
        <SourceHubEmailsTextFormatterMenu
          v-show="showTextFormattedMenu"
          class="absolute top-[-60px] left-0 !ml-0"
          :editor="editor"
          @click.stop=""
        />
        <div class="flex space-x-2">
          <input
            type="file"
            multiple
            @change="handleFileChangeLocal"
            class="hidden !ml-0"
            ref="fileInput"
          />
          <div
            class="hover:bg-[#F1F2F6] w-5 h-7 !ml-0 flex justify-center items-center rounded-md cursor-pointer"
            @click.stop="triggerFileInput"
          >
            <SharedIconHubEmailsAttach />
          </div>
          <button
            class="hover:bg-[#F1F2F6] w-7 h-7 flex justify-center items-center rounded-md cursor-pointer"
            @click.stop="showLinkPopup(), (showEmojiPicker = false)"
          >
            <SharedIconHubEmailsLinkIcon />
          </button>
          <div
            class="relative hover:bg-[#F1F2F6] w-7 h-7 flex justify-center items-center rounded-md cursor-pointer"
            :class="showEmojiPicker ? 'bg-[#F1F2F6]' : ''"
            @click.stop="toggleEmojiPicker"
          >
            <SharedIconHubEmailsEmojiIcon />
            <EmojiPicker
              v-if="showEmojiPicker"
              @click.stop=""
              :editor="editor"
              class="absolute top-[-344px] left-0"
            />
          </div>
          <div
            class="hover:bg-[#F1F2F6] w-7 h-7 flex justify-center items-center rounded-md cursor-pointer"
            @click="store.commit('emails/SET_DRIVE_INSERT_FILE_MODAL', true)"
          >
            <SharedIconHubEmailsDriveIcon />
          </div>
          <div
            class="hover:bg-[#F1F2F6] w-7 h-7 flex justify-center items-center rounded-md cursor-pointer"
            @click="store.commit('emails/SET_PHOTO_INSERT_FILE_MODAL', true)"
          >
            <SharedIconHubEmailsPhotoIcon />
          </div>
          <div
            class="hover:bg-[#F1F2F6] w-7 h-7 flex justify-center items-center rounded-md cursor-pointer"
            :class="showConfidentialModeModal ? 'bg-[#f1f2f6]' : ''"
            @click="store.commit('emails/SET_CONFIDENTIAL_MODE_MODAL', true)"
          >
            <SharedIconHubEmailsConfidentialIcon />
          </div>
          <div
            class="hover:bg-[#F1F2F6] w-7 h-7 flex justify-center items-center rounded-md cursor-pointer relative"
            :class="showSignatureMenu ? 'bg-[#F1F1F6]' : ''"
            @click.stop="
              (showSignatureMenu = !showSignatureMenu),
                (showMoreOptions = false),
                (showEmojiPicker = false)
            "
          >
            <SharedIconHubEmailsSignatureIcon />
            <LazySourceHubEmailsSignatureMenu
              @click.stop=""
              v-if="showSignatureMenu"
              :editor="editor"
            />
          </div>
        </div>
        <div
          class="relative hover:bg-[#F1F2F6] w-5 h-7 flex justify-center items-center rounded-md cursor-pointer"
          :class="showMoreOptions ? 'bg-[#F1F2F6]' : ''"
          @click.stop="
            (showMoreOptions = !showMoreOptions),
              (showSignatureMenu = false),
              (showEmojiPicker = false)
          "
        >
          <SharedIconHubEmailsThreeDotMenuIcon color="#525252" />
          <LazySourceHubEmailsMoreOptionsMenu
            v-if="showMoreOptions"
            @click.stop=""
          />
        </div>
      </div>
    </div>
    <div
      class="hover:bg-[#F1F2F6] w-7 h-7 flex justify-center items-center rounded-md cursor-pointer"
      @click="
        hideReplyBox(),
          deleteSpecificComposeItem(itemId),
          store.commit('emails/SET_SELECTED_COLOR', '')
      "
    >
      <SharedIconHubEmailsDeleteIcon />
    </div>
  </div>
</template>
