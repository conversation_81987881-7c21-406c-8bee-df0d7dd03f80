<script setup lang="ts">
import { useStore } from 'vuex'

const emit = defineEmits(['seleted-option'])
const props = withDefaults(
  defineProps<{
    searchText?: string
    itemId?: number
  }>(),
  {
    searchText: '',
    itemId: 0, // 👈 default value
  },
)

const store = useStore()
const recipientsSuggestionOptions = computed(
  () => store.state.emails.recipientsSuggestionOptions,
)
console.log(recipientsSuggestionOptions.value)
const searchedOptions = computed(() => {
  console.log(
    'recipientsSuggestionOptions.value',
    recipientsSuggestionOptions.value,
  )
  return recipientsSuggestionOptions.value.filter((option) =>
    option.text.toLowerCase().includes(props.searchText.toLowerCase()),
  )
})
</script>

<template>
  <div
    class="recipients-suggestion-menu bg-white w-[320px] h-fit max-h-[320px] custom-scroll"
  >
    <div v-if="searchedOptions && searchedOptions.length > 0" class="py-1">
      <div
        v-for="option in searchedOptions"
        :key="option.id"
        class="w-full py-1.5 hover:bg-[#F1F2F6] cursor-pointer"
        :class="option.selected ? ' opacity-50 !cursor-not-allowed' : ''"
        @click.stop="
          store.commit('emails/SET_RECIPIENTS_NAME', {
            id: itemId,
            recipientInfo: option,
          }),
            emit('seleted-option')
        "
      >
        <div class="flex space-x-4 items-center px-4">
          <img class="size-8" :src="option.image" :alt="option.text" />
          <div>
            <p class="text-sm text-[#333333] font-semibold">
              {{ option.text }}
            </p>
            <p class="text-xs text-[#525252]">{{ option.text }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.recipients-suggestion-menu {
  box-shadow: 0px 0px 8px #2228313d;
  border-radius: 8px;
}
</style>
