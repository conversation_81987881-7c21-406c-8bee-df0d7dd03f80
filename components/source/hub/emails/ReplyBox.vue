<script setup lang="ts">
import { useStore } from 'vuex'
import { Editor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import TextAlign from '@tiptap/extension-text-align'
import Highlight from '@tiptap/extension-highlight'
import TextStyle from '@tiptap/extension-text-style'
import Color from '@tiptap/extension-color'
import FontFamily from '@tiptap/extension-font-family'
import Underline from '@tiptap/extension-underline'
import Link from '@tiptap/extension-link'
import Table from '@tiptap/extension-table'
import TableRow from '@tiptap/extension-table-row'
import TableCell from '@tiptap/extension-table-cell'
import TableHeader from '@tiptap/extension-table-header'
import { CustomIndent } from '~/composables/tiptap-extension-indent'
import { FontSize } from '~/composables/FontSize'
import { Signature } from '~/composables/Signature'
import Image from '@tiptap/extension-image'

const store = useStore()
interface FileAttachment {
  attachedFiles: Array<File>
  fileUrls: Array<string>
  formatSize: Function
  uploadProgress: Array<number>
  removeFile: Function | string
}
interface Props {
  hideReplyBox?: (payload: MouseEvent) => void
  showForwardBox?: boolean
  forwardText?: string
  deleteSpecificComposeItem?: Function
  itemId?: number
  formatSize?: Function
  removeFile?: Function
  attachedFiles?: Array<File>
  fileUrls?: Array<string>
  uploadProgress?: Array<number>
  recipientInfo?: Array<any>
  uploadedComposeFiles?: Array<any>
  fileUploadEvents?: null | FileAttachment
  attachmentFromDrive?: null | FileAttachment
  linkFiles?: null
  insertPhotoFile?: string
  attachmentFromPhoto?: null | FileAttachment
}
const props = withDefaults(defineProps<Props>(), {
  hideReplyBox: () => {},
  showForwardBox: false,
  forwardText: '',
  deleteSpecificComposeItem: (id: number) => {},
  itemId: 0,
  formatSize: () => {},
  removeFile: () => {},
  attachedFiles: () => [],
  fileUrls: () => [],
  uploadProgress: () => [],
  fileUploadEvents: null,
  attachmentFromDrive: null,
  recipientInfo: () => [],
  uploadedComposeFiles: () => [],
  linkFiles: null,
  insertPhotoFile: '',
  attachmentFromPhoto: null,
})
const showGmailComposeModal = computed(
  () => store.state.emails.showGmailComposeModal,
)
const showComposeSection = computed(() => store.state.emails.showComposeSection)
const fileChanged = computed(() => store.state.emails.fileChanged)
const fileUrl = computed(() => store.state.emails.fileUrl)
const composeArray = computed(() => store.state.emails.composeArray)
const uploadedFiles = computed(() => store.state.emails.uploadedFiles)
const driveLinkFiles = computed(() => store.state.emails.driveLinkFiles)
// watch(
//   () => fileChanged.value,
//   (newValue) => {
//     console.log('fileChanged', newValue)
//     if (newValue) {
//       console.log('fileChanged')
//       editor.value
//         ?.chain()
//         .focus('end')
//         .setImage({ src: fileUrl.value })
//         .createParagraphNear() // 🪄 Like pressing "Enter"
//         .selectNodeForward()
//         .run()
//     }
//   },
// )

watch(
  () => props.insertPhotoFile,
  (newValue) => {
    if (newValue) {
      editor.value
        ?.chain()
        .focus('end')
        .setImage({ src: newValue })
        .createParagraphNear() // 🪄 Like pressing "Enter"
        .selectNodeForward()
        .run()
      store.commit('emails/SET_FILE_CHANGED', '')
    }
  },
)
watch(
  () => fileUrl.value,
  (newValue) => {
    if (newValue) {
      editor.value
        ?.chain()
        .focus('end')
        .setImage({ src: newValue })
        .createParagraphNear() // 🪄 Like pressing "Enter"
        .selectNodeForward()
        .run()
      store.commit('emails/SET_FILE_CHANGED', {
        fileChanged: false,
        fileUrl: '',
      })
    }
  },
)
const isTextSelected = ref(false)
const editor = ref<Editor | null>(null)
onMounted(() => {
  editor.value = new Editor({
    // attrs: { 'data-external': 'true' },
    content: props.showForwardBox ? `${props.forwardText}` : '',
    onSelectionUpdate({ editor }) {
      nextTick(() => {
        const { from, to } = editor.state.selection
        isTextSelected.value = from !== to && !editor.state.selection.empty
      })
    },
    extensions: [
      StarterKit.configure({
        table: false,
        blockquote: {
          HTMLAttributes: {
            class: 'border-l-2 border-gray-300 pl-2',
          },
        },
        bulletList: {
          HTMLAttributes: {
            class: 'list-disc ml-3',
          },
        },
        orderedList: {
          HTMLAttributes: {
            class: 'list-decimal ml-3',
          },
        },
      }),
      Signature,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      TextStyle.configure({ mergeNestedSpanStyles: true }),
      Color,
      Highlight.configure({
        multicolor: true,
      }),
      FontFamily.configure({
        types: ['textStyle'],
      }),
      Underline,
      CustomIndent.configure({
        types: ['heading', 'paragraph'],
        minIndent: 0,
        maxIndent: 1000,
        indentLevel: 20,
      }),
      FontSize,
      Link.configure({
        openOnClick: true,
        autolink: true,
        HTMLAttributes: {
          class: 'tiptap-link',
          style: 'color: #2563eb; text-decoration: underline; cursor: pointer;',
        },
      }), // Prevents auto-opening links
      Image,
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableCell,
      TableHeader,
    ],

    editorProps: {
      attributes: {
        class:
          'w-full h-auto no-scrollbar resize-none textarea-style outline-none border-none bg-white text-[#333333]',
      },
    },
  })
  editor.value?.setOptions({
    editorProps: {
      handleDOMEvents: {
        mouseup: () => {
          updatePopupPosition()
        },
      },
    },
  })
  document.addEventListener('click', () => {
    closelinkPopUp()
    recipientSelect.value = false
    selectedCc.value = false
    selectedBcc.value = false
    // linkPopupVisible.value = false
    // showLinkOptions.value = false
  })
})
onUnmounted(() => {
  document.removeEventListener('click', () => {
    closelinkPopUp()
    // linkPopupVisible.value = false
    // showLinkOptions.value = false
  })
})
const linkPopupVisible = ref(false)
const currentSelection = ref(null)
const linkMenuPosition = ref({ top: 0, left: 0 })
const textSelected = ref(false)
// Show Link Popup
const showLinkPopup = () => {
  updatePopupPosition()
  linkPopupVisible.value = true
}
const hideLinkPopup = () => {
  linkPopupVisible.value = false
}
const selectedLink = ref(null)
const showLinkOptions = ref(false) // Shows the first popup (Go to Link, Change, Remove)
const showEditLink = ref(false) // Shows the second popup (Link input)
const selectedLinks = ref(null) // Stores the selected link
const selectedText = ref<string | null | undefined>('')
const updatePopupPosition = () => {
  nextTick(() => {
    const selection = editor.value?.state.selection
    if (!selection) return

    const { from, to, empty } = selection
    textSelected.value = !empty // True if text is selected
    // Get link if the cursor is inside one
    const linkMark = editor.value?.getAttributes('link')
    selectedLink.value = linkMark?.href || null

    // if (textSelected.value || selectedLink.value) {
    const coords = editor.value?.view.coordsAtPos(from)
    const editorRect = editor.value?.view.dom.getBoundingClientRect()

    // const node = selection.anchorNode?.parentElement
    // const link = node?.closest('a') // Find nearest <a> tag
    // console.log('link', link, node, linkMark)
    if (linkMark?.href && !empty) {
      selectedLinks.value = linkMark.href
      selectedText.value = empty
        ? ''
        : editor.value?.state.doc.textBetween(from, to, ' ')
      showLinkOptions.value = true // Show first popup
      showEditLink.value = false
    } else {
      selectedLinks.value = null
      showLinkOptions.value = false
    }
    if (coords && editorRect) {
      linkMenuPosition.value = {
        top: coords.top - editorRect.top + 30,
        left: coords.left - editorRect.left,
      }
    }
    // linkPopupVisible.value = true
    // } else {
    //   linkPopupVisible.value = false
    // }
  })
}

const editLink = () => {
  showLinkOptions.value = false
  linkPopupVisible.value = true
  textSelected.value = false
}

const removeLink = () => {
  editor.value?.chain().focus().extendMarkRange('link').unsetLink().run()
  showLinkOptions.value = false
}
// const isTextSelected = () => {
//   if (!editor.value) return false
//   return !editor.value.state.selection.empty
// }
const closelinkPopUp = () => {
  linkPopupVisible.value = false
  showLinkOptions.value = false
  showEditLink.value = false
  selectedLinks.value = null
  selectedText.value = null
}

// ✅ Handle keydown to unset link before typing
const onKeydown = (event: KeyboardEvent) => {
  if (!editor.value) return

  // Only handle Backspace key
  if (event.key === 'Backspace') return

  // We only want to unset the link if we're right after a link
  const { state } = editor.value
  const { from, empty } = state.selection
  if (!empty) return // only when caret, not selection

  const marks = state.storedMarks || state.doc.resolve(from).marks()
  const hasLink = marks.some((mark) => mark.type.name === 'link')

  if (hasLink) {
    editor.value.chain().focus().unsetMark('link').run()
  }
}
const inputModifierRef = ref<HTMLInputElement | null>(null)
const recipientsSuggestionInputRef = ref<HTMLInputElement | null>(null)
const ccSuggestionInputRef = ref<HTMLInputElement | null>(null)
const bccSuggestionInputRef = ref<HTMLInputElement | null>(null)
const recipientsSuggestionSearch = ref<string>('')
const ccSuggestionSearch = ref<string>('')
const bccSuggestionSearch = ref<string>('')
const handleSeletedOption = () => {
  recipientsSuggestionSearch.value = ''
  recipientsSuggestionInputRef.value.focus()
  recipientSelect.value = true
}
const recipientSelect = ref(false)
const ccSelect = ref(false)
const bccSelect = ref(false)
const selectedCc = ref(false)
const selectedBcc = ref(false)
</script>

<template>
  <div class="flex flex-col min-h-[312px]">
    <nav class="flex space-x-2 items-center">
      <div
        v-if="!showComposeSection"
        class="flex space-x-2 items-center pr-2 py-2.5 cursor-pointer rounded-md hover:bg-[#F1F2F6]"
      >
        <SharedIconHubEmailsForward
          v-if="showForwardBox"
          class="w-3.5 h-2.5 min-w-3.5 min-h-2.5"
        />
        <SharedIconHubEmailsReply
          v-else
          class="w-3.5 h-2.5 min-w-3.5 min-h-2.5"
        />
        <SharedIconHubEmailsDownArrow class="w-2 h-1.5" />
      </div>
      <div v-if="!showComposeSection">
        <p v-if="showForwardBox" class="text-[#707070]">Recipients</p>
        <p v-else class="text-[#333333]">
          Foodie Adventures
          <span class="text-[#707070]">&lt;<EMAIL>&gt;</span>
        </p>
      </div>
      <div v-if="showComposeSection" class="w-full text-[#707070]">
        <div
          class="flex flex-col items-start w-full py-0 border-b border-[#F1F2F6]"
        >
          <div class="flex items-start w-full py-0">
            <p
              v-if="
                recipientSelect || (recipientInfo && recipientInfo.length > 0)
              "
              class="text-[#707070] !mr-2 !mt-3"
            >
              To
            </p>
            <div class="flex flex-wrap items-center flex-grow">
              <template
                v-if="recipientInfo && recipientInfo.length > 0"
                class="flex flex-wrap items-center"
              >
                <div
                  v-for="(recipient, recipientIndex) in recipientInfo"
                  :key="recipient.id"
                  class="flex mr-4 mt-2 justify-between space-x-10 items-center text-sm pr-3 whitespace-nowrap"
                  :class="
                    recipient.name && recipientSelect
                      ? 'bg-white rounded-full border border-[#707070] text-[#333333] font-semibold'
                      : 'text-[#333333] !mr-0 !pr-1'
                  "
                >
                  <div
                    v-if="recipient.name"
                    class="flex space-x-2 items-center"
                  >
                    <img
                      v-if="recipientSelect"
                      class="size-7"
                      :src="recipient.image"
                      :alt="recipient.text"
                    />
                    <p>
                      {{ recipient.name
                      }}{{
                        recipientSelect ||
                        recipientInfo.length - 1 === recipientIndex
                          ? ''
                          : ','
                      }}
                    </p>
                  </div>
                  <p v-else>
                    {{ recipient.text
                    }}{{
                      recipientSelect ||
                      recipientInfo.length - 1 === recipientIndex
                        ? ''
                        : ','
                    }}
                  </p>
                  <ClientOnly>
                    <fa
                      v-if="recipient.name && recipientSelect"
                      class="text-[#333333] cursor-pointer"
                      :icon="['fas', 'times']"
                      @click.stop="
                        store.commit(
                          'emails/SET_REMOVE_SPECIFIC_RECIPIENTS_NAME',
                          {
                            id: itemId,
                            recipientInfoId: recipient.id,
                          },
                        ),
                          (recipientsSuggestionSearch = ''),
                          recipientsSuggestionInputRef.focus()
                      "
                    />
                  </ClientOnly>
                </div>
              </template>
              <div class="relative mt-2">
                <input
                  ref="recipientsSuggestionInputRef"
                  class="w-full outline-none border-none"
                  type="text"
                  :placeholder="
                    recipientSelect ||
                    (recipientInfo && recipientInfo.length > 0)
                      ? ''
                      : 'Recipient'
                  "
                  v-model="recipientsSuggestionSearch"
                  @click.stop="recipientSelect = true"
                />
                <SourceHubEmailsRecipientsSuggestionMenu
                  v-if="recipientsSuggestionSearch"
                  class="absolute top-full left-0 z-1"
                  :search-text="recipientsSuggestionSearch"
                  :itemId="itemId"
                  @seleted-option="handleSeletedOption"
                />
              </div>
            </div>
            <p
              v-if="
                (recipientSelect ||
                  (recipientInfo && recipientInfo.length > 0)) &&
                !selectedCc
              "
              class="text-[#525252] !mr-2 !mt-3 cursor-pointer"
              @click.stop="selectedCc = true"
            >
              Cc
            </p>
            <p
              v-if="
                (recipientSelect ||
                  (recipientInfo && recipientInfo.length > 0)) &&
                !selectedBcc
              "
              class="text-[#525252] !mr-0 !mt-3 cursor-pointer"
              @click.stop="selectedBcc = true"
            >
              Bcc
            </p>
          </div>

          <div v-if="selectedCc" class="flex items-start w-full py-0">
            <p
              v-if="
                recipientSelect || (recipientInfo && recipientInfo.length > 0)
              "
              class="text-[#707070] !mr-2 !mt-1"
            >
              Cc
            </p>
            <div class="flex flex-wrap items-center flex-grow">
              <!-- <template
                v-if="recipientInfo && recipientInfo.length > 0"
                class="flex flex-wrap items-center"
              >
                <div
                  v-for="(recipient, recipientIndex) in recipientInfo"
                  :key="recipient.id"
                  class="flex mr-4 mt-2 justify-between space-x-10 items-center text-sm pr-3 whitespace-nowrap"
                  :class="
                    recipient.name && ccSelect
                      ? 'bg-white rounded-full border border-[#707070] text-[#333333] font-semibold'
                      : 'text-[#333333] !mr-0 !pr-1'
                  "
                >
                  <div
                    v-if="recipient.name"
                    class="flex space-x-2 items-center"
                  >
                    <img
                      v-if="ccSelect"
                      class="size-7"
                      :src="recipient.image"
                      :alt="recipient.text"
                    />
                    <p>
                      {{ recipient.name
                      }}{{
                        ccSelect ||
                        recipientInfo.length - 1 === recipientIndex
                          ? ''
                          : ','
                      }}
                    </p>
                  </div>
                  <p v-else>
                    {{ recipient.text
                    }}{{
                      ccSelect ||
                      recipientInfo.length - 1 === recipientIndex
                        ? ''
                        : ','
                    }}
                  </p>
                  <ClientOnly>
                    <fa
                      v-if="recipient.name && ccSelect"
                      class="text-[#333333] cursor-pointer"
                      :icon="['fas', 'times']"
                      @click.stop="
                        store.commit(
                          'emails/SET_REMOVE_SPECIFIC_RECIPIENTS_NAME',
                          {
                            id: itemId,
                            recipientInfoId: recipient.id,
                          },
                        ),
                          (recipientsSuggestionSearch = ''),
                          recipientsSuggestionInputRef.focus()
                      "
                    />
                  </ClientOnly>
                </div>
              </template> -->
              <div class="relative mt-0">
                <input
                  ref="ccSuggestionInputRef"
                  class="w-full outline-none border-none"
                  type="text"
                  v-model="ccSuggestionSearch"
                  @click.stop="ccSelect = true"
                />
                <!-- <SourceHubEmailsRecipientsSuggestionMenu
                  v-if="recipientsSuggestionSearch"
                  class="absolute top-full left-0 z-1"
                  :search-text="recipientsSuggestionSearch"
                  :itemId="itemId"
                  @seleted-option="handleSeletedOption"
                /> -->
              </div>
            </div>
          </div>
          <div v-if="selectedBcc" class="flex items-start w-full py-0">
            <p
              v-if="bccSelect || (recipientInfo && recipientInfo.length > 0)"
              class="text-[#707070] !mr-2 !mt-1"
            >
              Bcc
            </p>
            <div class="flex flex-wrap items-center flex-grow">
              <!-- <template
                v-if="recipientInfo && recipientInfo.length > 0"
                class="flex flex-wrap items-center"
              >
                <div
                  v-for="(recipient, recipientIndex) in recipientInfo"
                  :key="recipient.id"
                  class="flex mr-4 mt-2 justify-between space-x-10 items-center text-sm pr-3 whitespace-nowrap"
                  :class="
                    recipient.name && bccSelect
                      ? 'bg-white rounded-full border border-[#707070] text-[#333333] font-semibold'
                      : 'text-[#333333] !mr-0 !pr-1'
                  "
                >
                  <div
                    v-if="recipient.name"
                    class="flex space-x-2 items-center"
                  >
                    <img
                      v-if="bccSelect"
                      class="size-7"
                      :src="recipient.image"
                      :alt="recipient.text"
                    />
                    <p>
                      {{ recipient.name
                      }}{{
                        bccSelect ||
                        recipientInfo.length - 1 === recipientIndex
                          ? ''
                          : ','
                      }}
                    </p>
                  </div>
                  <p v-else>
                    {{ recipient.text
                    }}{{
                      bccSelect ||
                      recipientInfo.length - 1 === recipientIndex
                        ? ''
                        : ','
                    }}
                  </p>
                  <ClientOnly>
                    <fa
                      v-if="recipient.name && bccSelect"
                      class="text-[#333333] cursor-pointer"
                      :icon="['fas', 'times']"
                      @click.stop="
                        store.commit(
                          'emails/SET_REMOVE_SPECIFIC_RECIPIENTS_NAME',
                          {
                            id: itemId,
                            recipientInfoId: recipient.id,
                          },
                        ),
                          (recipientsSuggestionSearch = ''),
                          recipientsSuggestionInputRef.focus()
                      "
                    />
                  </ClientOnly>
                </div>
              </template> -->
              <div class="relative mt-0">
                <input
                  ref="bccSuggestionInputRef"
                  class="w-full outline-none border-none"
                  type="text"
                  v-model="bccSuggestionSearch"
                  @click.stop="bccSelect = true"
                />
                <!-- <SourceHubEmailsRecipientsSuggestionMenu
                  v-if="recipientsSuggestionSearch"
                  class="absolute top-full left-0 z-1"
                  :search-text="recipientsSuggestionSearch"
                  :itemId="itemId"
                  @seleted-option="handleSeletedOption"
                /> -->
              </div>
            </div>
          </div>
        </div>
        <div class="w-full pt-2 pr-4 mt-1.5 border-b border-[#F1F2F6]">
          <input
            class="w-full outline-none border-none"
            type="text"
            placeholder="Subject"
          />
        </div>
      </div>
    </nav>
    <div class="mt-3 flex-grow relative" @click="closelinkPopUp()">
      <!-- <textarea
        class="w-full h-auto no-scrollbar resize-none textarea-style outline-none border-none bg-white text-[#333333]"
        name=""
        id=""
      ></textarea> @click.stop="" -->
      <editor-content :editor="editor" @keydown="onKeydown" @click.stop="" />
      <!-- Floating Link Popup -->
      <LinkInputPopup
        v-if="linkPopupVisible"
        :editor="editor"
        :selection="currentSelection"
        :position="linkMenuPosition"
        :textSelected="textSelected"
        :selectedLinks="selectedLinks"
        :selectedText="selectedText"
        @close="closelinkPopUp()"
        @click.stop=""
      />
      <div
        v-if="showLinkOptions"
        @click.stop=""
        :style="`position: absolute; top: ${linkMenuPosition.top}px; left: ${linkMenuPosition.left}px; z-index: 100; background: #ffffff; border: 1px solid; border-color: #bbb #bbb #a8a8a8; border-radius: 2px; padding: 2px 10px; box-shadow: 0 1px 3px rgba(0, 0, 0, .2);`"
      >
        <span class="text-[13px] font-medium"
          >Go to link:
          <a
            :href="selectedLinks"
            target="_blank"
            class="text-[#15c] font-medium hover:underline"
            >{{ selectedLinks }}</a
          >
        </span>
        |
        <button
          @click="editLink"
          class="text-[#15c] text-[13px] font-medium hover:underline"
        >
          Change
        </button>
        |
        <button
          @click="removeLink"
          class="text-[#15c] text-[13px] font-medium hover:underline"
        >
          Remove
        </button>
      </div>
      <LazySourceHubAttachmentFileContainer
        v-if="
          composeArray &&
          composeArray.length > 0 &&
          uploadedComposeFiles &&
          uploadedComposeFiles.length
        "
        :attachedFiles="uploadedComposeFiles"
      />
      <LazySourceHubAttachmentFileContainer
        v-else-if="uploadedFiles && uploadedFiles.length > 0"
        :attachedFiles="uploadedFiles"
      />
      <LazySourceHubAttachmentFileContainer
        v-else-if="driveLinkFiles && driveLinkFiles.length > 0"
        removeFile="removeDriveLink"
        :attachedFiles="driveLinkFiles"
      />
      <LazySourceHubAttachmentFileContainer
        v-else-if="linkFiles && linkFiles.length > 0"
        removeFile="removeComposeDriveLink"
        :attachedFiles="linkFiles"
      />
      <!-- <SourceHubEmailsLinkAdding /> -->
    </div>

    <LazySourceHubAttachmentFileContainer
      v-if="fileUploadEvents && fileUploadEvents.attachedFiles.length"
      :attachedFiles="fileUploadEvents?.attachedFiles"
      :fileUrls="fileUploadEvents?.fileUrls"
      :formatSize="fileUploadEvents?.formatSize"
      :removeFile="fileUploadEvents?.removeFile"
      :uploadProgress="fileUploadEvents?.uploadProgress"
    />
    <LazySourceHubAttachmentFileContainer
      v-if="
        (attachmentFromDrive && attachmentFromDrive.attachedFiles.length) ||
        (attachmentFromPhoto && attachmentFromPhoto.attachedFiles.length)
      "
      :attachedFiles="
        attachmentFromDrive?.attachedFiles || attachmentFromPhoto?.attachedFiles
      "
      :fileUrls="attachmentFromDrive?.fileUrls || attachmentFromPhoto?.fileUrls"
      :formatSize="
        attachmentFromDrive?.formatSize || attachmentFromPhoto?.formatSize
      "
      :removeFile="
        attachmentFromDrive?.removeFile || attachmentFromPhoto?.removeFile
      "
      :uploadProgress="
        attachmentFromDrive?.uploadProgress ||
        attachmentFromPhoto?.uploadProgress
      "
    />
    <LazySourceHubAttachmentFileContainer
      v-else-if="
        inputModifierRef &&
        inputModifierRef.attachedFiles.length &&
        !showGmailComposeModal
      "
      :attachedFiles="inputModifierRef?.attachedFiles"
      :fileUrls="inputModifierRef?.fileUrls"
      :formatSize="inputModifierRef?.formatSize"
      :removeFile="inputModifierRef?.removeFile"
      :uploadProgress="inputModifierRef?.uploadProgress"
    />
    <div
      v-if="!showComposeSection"
      class="w-[30px] h-3.5 bg-[#F1F2F6] rounded-[5px] flex justify-center items-center"
    >
      <SharedIconHubEmailsThreeDotMenuIcon
        class="transform rotate-90"
        color="#525252"
      />
    </div>
    <div class="mt-4">
      <SourceHubEmailsInputModifier
        ref="inputModifierRef"
        :hideReplyBox="hideReplyBox"
        :deleteSpecificComposeItem="deleteSpecificComposeItem"
        :itemId="itemId"
        :editor="editor"
        :showLinkPopup="showLinkPopup"
        :hideLinkPopup="hideLinkPopup"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
// .reply-box {
//   background: #ffffff 0% 0% no-repeat padding-box;
//   box-shadow: 1px 2px 8px #00000029;
//   border-radius: 8px;
// }
.textarea-style {
  field-sizing: content;
  width: 100%;
}
</style>
