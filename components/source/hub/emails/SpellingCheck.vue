<script setup lang="ts">
import { useStore } from 'vuex'
interface Languages {
  id: number
  text: string
}

const store = useStore()
const spellingCheckRef = ref<HTMLElement | null>(null)

const handleCheckSpelling = () => {
  console.log('Check Spelling')
}

const languages: Languages[] = [
  { id: 0, text: 'Auto' },
  { id: 1, text: 'Afrikaans' },
  { id: 2, text: 'Azerbaycanca' },
  { id: 3, text: 'Bahasa Indonesia' },
  { id: 4, text: 'Bahasa Melayu' },
  { id: 5, text: 'Català' },
  { id: 6, text: '<PERSON><PERSON><PERSON><PERSON>' },
  { id: 7, text: '<PERSON><PERSON>rae<PERSON>' },
  { id: 8, text: 'Dansk' },
  { id: 9, text: '<PERSON>uts<PERSON>' },
  { id: 10, text: '<PERSON><PERSON><PERSON> keel' },
  { id: 11, text: 'English (UK)' },
  { id: 12, text: 'English (US)' },
  { id: 13, text: '<PERSON>spañol' },
  { id: 14, text: '<PERSON><PERSON>a<PERSON>l (Latinoamérica)' },
]

const showLanguageDropdown = ref(false)
const checkedLanguage = ref<Languages>(languages[0])

const toggleLanguageDropdown = () => {
  showLanguageDropdown.value = !showLanguageDropdown.value
}
const handleSelectSpellingLanguage = (language: Languages) => {
  checkedLanguage.value = language
  showLanguageDropdown.value = false
}

const handleClickOutside = (event: MouseEvent) => {
  if (
    spellingCheckRef.value &&
    showLanguageDropdown.value &&
    !spellingCheckRef.value.contains(event.target as Node)
  ) {
    showLanguageDropdown.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<template>
  <div
    ref="spellingCheckRef"
    class="absolute h-12 bottom-17.8 right-4 rounded-lg shadow-[0px_0px_8px_#2228313D] flex items-center justify-center z-10"
  >
    <ul
      v-if="showLanguageDropdown"
      class="absolute bottom-full py-1 right-10 shadow-[0px_0px_8px_#2228313D] rounded-lg min-w-[250px] max-h-[400px] overflow-y-auto z-10 custom-scroll"
    >
      <li
        v-for="(language, idx) in languages"
        :key="language.id"
        class="space-x-2.5 bg-white text-[#525252] px-4 h-9 first:rounded-t-lg last:rounded-b-lg flex items-center w-full hover:bg-[#F1F2F6] cursor-pointer"
        :class="idx === 0 ? 'border-b border-[#C2C2C2]/50' : ''"
        @click="handleSelectSpellingLanguage(language)"
      >
        <SharedIconCheckIcon
          v-if="checkedLanguage.id === language.id"
          class="w-4 h-3"
        />
        <div v-else class="w-4 h-3"></div>
        <span class="text-base leading-[21px] text-[#525252]"
          >{{ language.text }}
        </span>
      </li>
    </ul>
    <button
      class="text-[#525252] text-base leading-[24px] font-medium px-4 h-full hover:bg-[#F1F2F6]"
      @click="handleCheckSpelling"
    >
      Recheck
    </button>
    <button
      class="px-2 h-full hover:bg-[#F1F2F6] flex justify-center items-center"
      @click="toggleLanguageDropdown"
    >
      <fa class="text-[#525252]" :icon="['fas', 'caret-down']" />
    </button>
    <button
      class="text-[#525252] px-3 h-full hover:bg-[#F1F2F6] flex justify-center items-center"
      @click.stop="store.commit('emails/SET_SHOW_SPELLING_CHECK_MODAL')"
    >
      <SharedIconHubEmailsCrossIcon class="w-4 h-4" />
    </button>
  </div>
</template>

<style scoped></style>
