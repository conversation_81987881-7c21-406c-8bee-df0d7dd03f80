<script setup lang="ts">
import { format, parseISO } from 'date-fns'
import Docs from '~/components/shared/icon/hub/emails/Docs.vue'
import Folder from '~/components/shared/icon/hub/emails/Folder.vue'
import Image from '~/components/shared/icon/hub/emails/Image.vue'
import Pdf from '~/components/shared/icon/hub/emails/Pdf.vue'
import type { InsertFiles } from '~/types/gmail'

interface Option {
  id: number
  text: string
  value: string
}

const props = withDefaults(
  defineProps<{
    allFiles: Record<string, InsertFiles[]>
    isAscSorting: boolean
    selectedButton: string
  }>(),
  {
    allFiles: () => ({}),
    isAscSorting: true,
    selectedButton: '',
  },
)

const emit = defineEmits<{
  (event: 'selectedInsertFile', files: any, id: number): void
  (event: 'sorting'): void
}>()

const filterOptions = computed(() => {
  const baseOptions = [
    {
      id: 2,
      text: 'Last modified',
      value: 'last_modified',
    },
    {
      id: 3,
      text: 'Last modified by me',
      value: 'last_modified_by_me',
    },
    {
      id: 4,
      text: 'Last opened by me',
      value: 'last_opened_by_me',
    },
  ]
  const shareDateOption = {
    id: 5,
    text: 'Shared date',
    value: 'shared_date',
  }
  if (props.selectedButton === 'My Drive') {
    return [...baseOptions]
  } else if (props.selectedButton === 'Shared with me') {
    return [...baseOptions, shareDateOption]
  }
  return baseOptions
})

const selectedFilter = defineModel<Option>({
  default: {
    id: 2,
    text: 'Last modified',
    value: 'last_modified',
  },
})
const isFilterDropDownOpen = ref(false)

const localFilter = ref<Option | null>(null)

const formatDate = (utcDate: string) => {
  const parsedDate = parseISO(utcDate)
  return format(parsedDate, 'MMM dd, yyyy')
}
const handleChangeTableFilter = (option: Option) => {
  if (localFilter) {
    selectedFilter.value = option
  }
}
watch(selectedFilter, (newVal) => {
  if (newVal.value === 'name') {
    localFilter.value = null
  } else {
    localFilter.value = newVal
  }
})
</script>

<template>
  <div class="overflow-y-auto max-h-[calc(100%-200px)] custom-scroll pb-[68px]">
    <table class="w-full !border-separate !border-spacing-0">
      <thead class="sticky top-0 z-10">
        <tr class="text-left text-base bg-white text-[#525252] font-medium">
          <th class="pl-6 pr-4 py-[9px] flex items-center gap-4">
            <button
              class="h-full leading-9"
              @click.stop="
                selectedFilter = { id: 1, text: 'Name', value: 'name' }
              "
            >
              Name
            </button>
            <button
              v-if="selectedFilter.value === 'name'"
              class="w-9 h-9 flex justify-center items-center hover:bg-[#F1F2F6] rounded-full transition-all duration-200 ease-in-out"
              :class="[isAscSorting ? 'rotate-90' : 'rotate-[270deg]']"
              @click.stop="emit('sorting')"
            >
              <SharedIconHubEmailsBackArrow />
            </button>
          </th>
          <th class="px-4 py-[9px]">Owner</th>
          <th class="px-4 py-[9px] flex items-center gap-2 group w-[280px]">
            <div class="flex items-center gap-2">
              <BaseDropsDown
                class="rounded-full w-auto language-dropdown"
                dropdownPosition="absolute right-0"
                selectedOptionClass="text-[#525252] font-semibold"
                triggerBtnClass="!px-0 !font-semibold"
                v-model="localFilter"
                :options="filterOptions"
                labelKey="text"
                placeholder="Last modified"
                :menuWidth="'100%'"
                :menuHeight="36"
                :dropdownWidth="204"
                :dropdownMaxHeight="400"
                :menuBgColor="'#fff'"
                menuTextColor="#525252"
                dropsdownTextColor="#525252"
                scrollbarTrackColor="#a1cdff50"
                scrollbarThumbColor="#a1cdff"
                hoverColor="#F1F2F6"
                hoverTextColor="#525252"
                hideArrow
                :showSelectedIcon="true"
                :isOpenMenu="isFilterDropDownOpen"
                @open="(isOpen: boolean) => (isFilterDropDownOpen = isOpen)"
                @change="handleChangeTableFilter"
              />
              <fa
                class="text-xl transition-all duration-300 ease-in-out text-[#4A71D4] cursor-pointer"
                :class="[
                  isFilterDropDownOpen
                    ? 'rotate-180'
                    : 'group-hover:block hidden',
                ]"
                :icon="['fas', 'caret-down']"
                @click.stop="isFilterDropDownOpen = !isFilterDropDownOpen"
              />
              <div
                class="w-3 h-1"
                :class="[
                  !isFilterDropDownOpen ? 'group-hover:hidden block' : 'hidden',
                ]"
              ></div>
            </div>
            <button
              v-if="selectedFilter.value !== 'name'"
              class="w-9 h-9 flex justify-center items-center hover:bg-[#F1F2F6] rounded-full transition-all duration-200 ease-in-out"
              :class="[isAscSorting ? 'rotate-90' : 'rotate-[270deg]']"
              @click.stop="emit('sorting')"
            >
              <SharedIconHubEmailsBackArrow />
            </button>
          </th>
          <th class="pl-4 pr-6 py-[9px]">File size</th>
        </tr>
      </thead>
      <tbody>
        <template v-for="(files, category) in allFiles" :key="category">
          <tr
            v-for="(file, idx) in files"
            tabindex="1"
            :key="idx"
            class="text-base focus:bg-[#e0e0e3] group text text-[#525252]"
            :class="[
              file.select ? 'bg-[#e3efff]' : 'bg-white hover:bg-[#F1F2F6]',
            ]"
            @click.stop="emit('selectedInsertFile', files, file.id)"
          >
            <td
              class="pl-6 pr-4 py-[15px] flex items-center gap-2 group-focus:!border-y group-focus:!border-[#4A71D4]"
            >
              <Image v-if="file.type === 'image/png'" class="text-[#E21F3F]" />
              <Docs v-else-if="file.type === 'docs'" class="text-[#4A71D4]" />
              <Pdf
                v-else-if="file.type === 'application/pdf'"
                class="text-[#EA4235]"
              />
              <Folder
                v-else-if="file.type === 'folder'"
                class="text-[#707070]"
              />
              {{ file.title }}
            </td>
            <td
              class="px-4 py-[15px] group-focus:!border-y group-focus:!border-[#4A71D4]"
            >
              {{ file.owner }}
            </td>
            <td
              class="px-4 py-[15px] group-focus:!border-y group-focus:!border-[#4A71D4] w-[280px]"
            >
              {{ formatDate(file.date) }}
            </td>
            <td
              class="pl-4 pr-6 py-[15px] group-focus:!border-y group-focus:!border-[#4A71D4]"
            >
              {{ file.size ? `${file.size} KB` : '-' }}
            </td>
          </tr>
        </template>
      </tbody>
    </table>
  </div>
</template>

<style scoped>
th,
td {
  border-bottom: 1px solid #f1f1f2 !important;
  border-top: 1px solid transparent !important;
}
</style>
