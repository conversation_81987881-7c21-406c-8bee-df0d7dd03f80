<script setup lang="ts">
import {
  endOfWeek,
  isAfter,
  isBefore,
  isThisMonth,
  isThisWeek,
  isToday,
  isYesterday,
  startOfWeek,
  subMonths,
  subWeeks,
} from 'date-fns'
import type { InsertFiles } from '~/types/gmail'
import FileTable from './FileTable.vue'
import FolderCard from './FolderCard.vue'
import ImageCard from './ImageCard.vue'
import RecentTable from './RecentTable.vue'

type SelectedButton = 'Recent' | 'My Drive' | 'Shared with me' | 'Upload'

const emit = defineEmits<{
  (event: 'resetAllSelectedFiles'): void
  (event: 'selectedInsertFile', files: any, id: number): void
  (event: 'selectedFilter', filter: any): void
}>()

const props = withDefaults(
  defineProps<{
    insertFiles: InsertFiles[]
    toggleListGrid: boolean
    selectedButton: SelectedButton
  }>(),
  {
    insertFiles: () => [],
    toggleListGrid: false,
    selectedButton: 'Recent',
  },
)

const { toggleListGrid, selectedButton, insertFiles } = toRefs(props)

const isAscSorting = defineModel<boolean>({ default: true })

const handleAscSorting = () => {
  isAscSorting.value = !isAscSorting.value
}

const filterOptions = computed(() => {
  const baseOptions = [
    {
      id: 1,
      text: 'Name',
      value: 'name',
    },
    {
      id: 2,
      text: 'Last modified',
      value: 'last_modified',
    },
    {
      id: 3,
      text: 'Last modified by me',
      value: 'last_modified_by_me',
    },
    {
      id: 4,
      text: 'Last opened by me',
      value: 'last_opened_by_me',
    },
  ]
  const shareDateOption = {
    id: 5,
    text: 'Shared date',
    value: 'shared_date',
  }
  if (selectedButton.value === 'My Drive') {
    return [...baseOptions]
  } else if (selectedButton.value === 'Shared with me') {
    return [...baseOptions, shareDateOption]
  }
  return baseOptions
})
const selectedFilter = ref(filterOptions.value[0])
const isFilterDropDownOpen = ref(false)

const recentFiles = computed(() => {
  const today = new Date()
  const startOfThisWeek = startOfWeek(today, { weekStartsOn: 1 }) // Monday start
  const startOfLastWeek = startOfWeek(subWeeks(today, 1), { weekStartsOn: 1 })
  const endOfLastWeek = endOfWeek(subWeeks(today, 1), { weekStartsOn: 1 })
  const startOfLastMonth = subMonths(today, 1)

  const parseUTCDate = (dateStr: string) => new Date(dateStr)

  return {
    Today: insertFiles.value.filter(
      (file: InsertFiles) =>
        file.type !== 'folder' && isToday(parseUTCDate(file.date)),
    ),
    Yesterday: insertFiles.value.filter(
      (file: InsertFiles) =>
        file.type !== 'folder' && isYesterday(parseUTCDate(file.date)),
    ),
    'Earlier this week': insertFiles.value.filter((file: InsertFiles) => {
      const d = parseUTCDate(file.date)
      return (
        file.type !== 'folder' &&
        isThisWeek(d, { weekStartsOn: 1 }) &&
        !isToday(d) &&
        !isYesterday(d)
      )
    }),
    'Last week': insertFiles.value.filter((file: InsertFiles) => {
      const d = parseUTCDate(file.date)
      return (
        file.type !== 'folder' &&
        isAfter(d, startOfLastWeek) &&
        isBefore(d, endOfLastWeek)
      )
    }),
    'Last month': insertFiles.value.filter((file: InsertFiles) => {
      const d = parseUTCDate(file.date)
      return (
        file.type !== 'folder' && isThisMonth(d) && isBefore(d, startOfLastWeek)
      )
    }),
    Older: insertFiles.value.filter(
      (file: InsertFiles) =>
        file.type !== 'folder' &&
        isBefore(parseUTCDate(file.date), startOfLastMonth),
    ),
  }
})

const driveFiles = computed(() => {
  return {
    Folders: insertFiles.value.filter(
      (file: InsertFiles) => file.type === 'folder',
    ),
    Files: insertFiles.value.filter(
      (file: InsertFiles) => file.type !== 'folder',
    ),
  }
})

const commonsFiles = computed(() => {
  const today = new Date()
  const startOfThisWeek = startOfWeek(today, { weekStartsOn: 1 }) // Monday start
  const startOfLastWeek = startOfWeek(subWeeks(today, 1), { weekStartsOn: 1 })
  const endOfLastWeek = endOfWeek(subWeeks(today, 1), { weekStartsOn: 1 })
  const startOfLastMonth = subMonths(today, 1)

  // Convert UTC string to local Date
  const parseUTCDate = (dateStr: string) => new Date(dateStr)

  return {
    Today: insertFiles.value.filter((file: InsertFiles) =>
      isToday(parseUTCDate(file.date)),
    ),
    Yesterday: insertFiles.value.filter((file: InsertFiles) =>
      isYesterday(parseUTCDate(file.date)),
    ),
    'Earlier this week': insertFiles.value.filter((file: InsertFiles) => {
      const d = parseUTCDate(file.date)
      return (
        isThisWeek(d, { weekStartsOn: 1 }) && !isToday(d) && !isYesterday(d)
      )
    }),
    'Last week': insertFiles.value.filter((file: InsertFiles) => {
      const d = parseUTCDate(file.date)
      return isAfter(d, startOfLastWeek) && isBefore(d, endOfLastWeek)
    }),
    'Last month': insertFiles.value.filter((file: InsertFiles) => {
      const d = parseUTCDate(file.date)
      return isThisMonth(d) && isBefore(d, startOfLastWeek)
    }),
    Older: insertFiles.value.filter((file: InsertFiles) =>
      isBefore(parseUTCDate(file.date), startOfLastMonth),
    ),
  }
})

const filesArray = computed<Record<string, InsertFiles[]>>(() => {
  if (selectedButton.value === 'Recent') {
    return recentFiles.value
  } else if (selectedButton.value === 'My Drive') {
    if (selectedFilter.value.value === 'name') {
      return driveFiles.value
    } else {
      return commonsFiles.value
    }
  } else if (selectedButton.value === 'Shared with me') {
    if (selectedFilter.value.value === 'name') {
      return driveFiles.value
    } else {
      return commonsFiles.value
    }
  }
  return commonsFiles.value
})

watch(
  [selectedFilter, isAscSorting],
  ([newFilter, newSort]) => {
    emit('selectedFilter', newFilter)
  },
  { immediate: true },
)

watch(
  [() => selectedButton.value],
  ([button]) => {
    if (button === 'Recent') {
      selectedFilter.value = filterOptions.value[filterOptions.value.length - 1]
    } else if (button === 'My Drive') {
      selectedFilter.value = filterOptions.value[0]
    } else if (button === 'Shared with me') {
      selectedFilter.value = filterOptions.value[filterOptions.value.length - 1]
    }
  },
  { immediate: true },
)
</script>
<template>
  <div class="w-full h-full">
    <div
      v-if="!toggleListGrid"
      class="px-6 py-5 flex flex-col gap-6 h-[calc(100%-200px)] custom-scroll overflow-y-auto relative pb-[68px]"
      @click="emit('resetAllSelectedFiles')"
    >
      <div
        v-if="selectedButton !== 'Recent'"
        class="flex space-x-2 items-center absolute top-4 right-6"
      >
        <BaseDropsDown
          class="rounded-full language-dropdown w-auto"
          dropdownPosition="absolute right-0"
          triggerBtnClass="hover:bg-[#F1F2F6]"
          v-model="selectedFilter"
          :options="filterOptions"
          labelKey="text"
          placeholder="HTML"
          :menuWidth="'100%'"
          :menuHeight="36"
          :dropdownWidth="204"
          :dropdownMaxHeight="400"
          :menuBgColor="!isFilterDropDownOpen ? '' : '#F1F2F6'"
          menuTextColor="#707070"
          dropsdownTextColor="#525252"
          scrollbarTrackColor="#a1cdff50"
          scrollbarThumbColor="#a1cdff"
          hoverColor="#F1F2F6"
          hoverTextColor="#525252"
          hideArrow
          :showSelectedIcon="true"
          @open="(isOpen: boolean) => (isFilterDropDownOpen = isOpen)"
        />
        <button
          class="w-9 h-9 flex justify-center items-center hover:bg-[#F1F2F6] rounded-full transition-all duration-200 ease-in-out"
          :class="[isAscSorting ? 'rotate-90' : 'rotate-[270deg]']"
          @click="handleAscSorting"
        >
          <SharedIconHubEmailsBackArrow />
        </button>
      </div>
      <template v-for="(files, category) in filesArray" :key="category">
        <div v-if="files.length > 0" class="flex flex-col gap-4">
          <h3 class="text-[#707070]">
            {{ category }}
          </h3>
          <div
            class="grid grid-cols-[repeat(auto-fit,minmax(min(238px,100%),238px))] gap-2"
          >
            <template v-for="file in files" :key="file.id">
              <FolderCard
                v-if="selectedFilter.value === 'name' && file.type === 'folder'"
                :file="file"
                @selectFile="emit('selectedInsertFile', files, file.id)"
              />

              <ImageCard
                v-else
                :file="file"
                @selectFile="emit('selectedInsertFile', files, file.id)"
              />
            </template>
          </div>
        </div>
      </template>
    </div>
    <div v-else @click="emit('resetAllSelectedFiles')" class="h-full">
      <RecentTable
        v-if="selectedButton === 'Recent'"
        class="h-[calc(100%-200px)] overflow-y-auto custom-scroll pb-[68px]"
        :recentFiles="filesArray"
        @selectedInsertFile="
          (files, id) => emit('selectedInsertFile', files, id)
        "
      />
      <FileTable
        v-else
        :allFiles="filesArray"
        v-model="selectedFilter"
        :isAscSorting="isAscSorting"
        :selectedButton="selectedButton"
        @sorting="handleAscSorting"
        @selectedInsertFile="
          (files, id) => emit('selectedInsertFile', files, id)
        "
      />
    </div>
  </div>
</template>

<style scoped>
.file-box {
  background: white;
  border: 1px solid #c2c2c2;
  border-radius: 8px;
  opacity: 1;
}
.active-file-box {
  background: #e3efff;
  border: 1px solid #c2c2c2;
  border-radius: 8px;
  opacity: 1;
}
</style>
