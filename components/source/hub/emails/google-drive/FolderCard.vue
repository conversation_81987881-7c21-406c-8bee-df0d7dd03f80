<script setup lang="ts">
import Folder from '~/components/shared/icon/hub/emails/Folder.vue'
import type { InsertFiles } from '~/types/gmail'

defineProps<{
  file: InsertFiles
}>()

const emit = defineEmits<{
  (event: 'selectFile'): void
}>()

const handleSelectFile = () => {
  emit('selectFile')
}
</script>

<template>
  <div
    @click.stop="handleSelectFile"
    class="border rounded text-[#707070] px-4 py-[15px] flex items-center space-x-4 cursor-pointer bg-white"
    :class="[file.select ? 'border-[#707070]/80' : 'border-[#707070]/40']"
  >
    <Folder class="text-[#707070]" />
    <span class="text-base font-semibold leading-[20px]">{{ file.title }}</span>
  </div>
</template>

<style scoped></style>
