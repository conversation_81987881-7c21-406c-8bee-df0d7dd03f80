<script setup lang="ts">
import Docs from '~/components/shared/icon/hub/emails/Docs.vue'
import Folder from '~/components/shared/icon/hub/emails/Folder.vue'
import Image from '~/components/shared/icon/hub/emails/Image.vue'
import Pdf from '~/components/shared/icon/hub/emails/Pdf.vue'
import type { InsertFiles } from '~/types/gmail'

defineProps<{
  file: InsertFiles
}>()

const emit = defineEmits<{
  (event: 'selectFile'): void
}>()
</script>

<template>
  <div
    class="w-full max-h-[240px] h-[240px] cursor-pointer overflow-hidden relative flex flex-col"
    :class="file.select ? 'active-file-box' : 'file-box'"
    @click.stop="emit('selectFile')"
  >
    <div
      class="flex-1 w-full flex justify-center max-h-[calc(100%-56px)] bg-white"
      :class="[file.type === 'folder' ? 'items-center' : '']"
    >
      <Folder v-if="file.type === 'folder'" class="text-[#707070] size-15" />
      <img
        v-else
        class="w-full h-auto object-cover object-top"
        :src="file.img"
        :alt="file.title"
        width="100"
      />
    </div>
    <div
      class="w-full flex !space-x-2.5 items-center px-5 py-4"
      :class="file.select ? 'bg-[#e3efff]' : 'bg-white'"
    >
      <Image v-if="file.type === 'image/png'" class="text-[#E21F3F]" />
      <Docs v-else-if="file.type === 'docs'" class="text-[#4A71D4]" />
      <Pdf v-else-if="file.type === 'application/pdf'" class="text-[#EA4235]" />
      <Folder v-else-if="file.type === 'folder'" class="text-[#707070]" />
      <p class="text-[#707070]">{{ file.title }}</p>
    </div>
  </div>
</template>

<style scoped>
.file-box {
  background: white;
  border: 1px solid #c2c2c2;
  border-radius: 8px;
  opacity: 1;
}
.active-file-box {
  background: #e3efff;
  border: 1px solid #c2c2c2;
  border-radius: 8px;
  opacity: 1;
}
</style>
