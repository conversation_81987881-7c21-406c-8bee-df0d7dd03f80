<script setup lang="ts">
import type { InsertFiles } from '~/types/gmail'

const emit = defineEmits<{
  (event: 'selectedInsertFile', files: any, id: number): void
}>()

const props = withDefaults(
  defineProps<{
    recentFiles: Record<string, InsertFiles[]>
  }>(),
  {
    recentFiles: () => ({}),
  },
)
</script>

<template>
  <div class="w-full relative">
    <div
      class="sticky top-0 px-6 py-4 border-b border-[#f1f1f2] flex justify-between items-center bg-white z-10"
    >
      <p class="text-[#707070] font-semibold">Name</p>
    </div>
    <div
      v-for="(files, category) in recentFiles"
      :key="category"
      class="flex flex-col space-y-0"
      :class="files.length > 0 ? '' : '!mt-0'"
    >
      <div v-if="files.length > 0" class="px-6 py-4 border-b border-[#f1f1f2]">
        <h3 class="text-[#707070]">
          {{ category }}
        </h3>
      </div>
      <div v-if="files.length > 0" class="flex flex-col">
        <div
          :tabindex="file.id"
          class="w-full leading-[46px] px-6 py-3 flex !space-x-4 items-center cursor-pointer overflow-hidden relative"
          :class="
            file.select
              ? 'bg-[#e3efff] focus:border focus:border-[#4A71D4] border-y border-transparent'
              : 'bg-white hover:bg-[#F1F2F6] focus:bg-[#e0e0e3] focus:border-t focus:border-t-[#4A71D4] border border-b-[#f1f1f2] border-t-transparent border-l-transparent border-r-transparent last:border-b last:border-b-transparent'
          "
          v-for="file in files"
          :key="file.id"
          @click.stop="emit('selectedInsertFile', files, file.id)"
        >
          <div class="w-8 h-8">
            <img
              v-if="file.img"
              class="w-8 h-8"
              :src="file.img"
              :alt="file.title"
            />
          </div>
          <p class="text-[#707070]">{{ file.title }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
