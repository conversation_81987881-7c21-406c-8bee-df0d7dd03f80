export const FileUpload = () => {
  const MAX_FILE_SIZE_MB = 25
  const attachedFiles = ref<File[]>([])
  const fileUrls = ref<string[]>([])
  const uploadProgress = ref<number[]>([])
  const { $toast } = useNuxtApp()
  // Handle file input
  const handleFileChange = (attachments: Event) => {
    // const input = event.target as HTMLInputElement
    const files = attachments
    console.log(files, 'files')
    if (!files) return

    for (const file of files) {
      const fileSizeMB = file.size / (1024 * 1024)
      if (fileSizeMB > MAX_FILE_SIZE_MB) {
        $toast('error', {
          message: `File "${file.name}" exceeds the 25MB limit.`,
          className: 'toasted-bg-alert',
        })
        continue
      }
      const index = attachedFiles.value.length
      attachedFiles.value.push(file)
      console.log(fileSizeMB, attachedFiles.value)
      if (file.name.toLowerCase().endsWith('.eml')) {
        const newfile = new File(
          [file.from, file.subject, file.description], // file contents
          file.name, // file name
          { type: 'message/rfc822' }, // MIME type for .eml
        )
        const url = URL.createObjectURL(newfile)
        fileUrls.value.push(url)
      } else {
        console.log('url')
        const url = URL.createObjectURL(file)
        console.log(url)
        fileUrls.value.push(url)
      }
      uploadProgress.value.push(0)
      console.log(attachedFiles.value, fileUrls.value)
      // simulateUploadProgress(index) // Simulated for now
    }
  }

  // Simulate file upload progress (replace this with actual upload logic)
  // const simulateUploadProgress = (index: number) => {
  //   console.log(index, 'simulateUploadProgress')
  //   const interval = setInterval(() => {
  //     if (uploadProgress.value[index] >= 100) {
  //       clearInterval(interval)
  //     } else {
  //       console.log(index, 'simulateUploadProgress', uploadProgress.value[index])
  //       uploadProgress.value[index] += 10
  //     }
  //   }, 200)
  // }

  const removeFile = (index: number) => {
    URL.revokeObjectURL(fileUrls.value[index])
    attachedFiles.value.splice(index, 1)
    fileUrls.value.splice(index, 1)
    uploadProgress.value.splice(index, 1)
  }

  const formatSize = (size: number) => {
    const mb = size / (1024 * 1024)
    return mb >= 1 ? `${mb.toFixed(2)} M` : `${(size / 1024).toFixed(2)} K`
  }
  const resetFiles = () => {
    attachedFiles.value = []
    fileUrls.value = []
    uploadProgress.value = []
  }

  return {
    handleFileChange,
    removeFile,
    formatSize,
    attachedFiles,
    fileUrls,
    uploadProgress,
    resetFiles,
  }
}
