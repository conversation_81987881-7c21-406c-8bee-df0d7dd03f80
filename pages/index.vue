<template>
  <div class="landing_container md:pb-[88px] pb-26">
    <div class="main_body">
      <div class="body">
        <div id="hero_section" class="intro">
          <div class="intro_body lg:w-1/2 w-full min-h-[500px]">
            <h1>
              Compliance without complexity.
            </h1>
            <p>
              See how Sharp Archive can help you easily <br />monitor, capture,
              and store your emails and <br />communications with our data
              backup services.
            </p>
            <NuxtLink
              v-if="!loggedIn"
              class="orange_button"
              to="/appointment"
              aria-label="Book a Demo"
            >
              Book a Demo
            </NuxtLink>
            <p v-if="!loggedIn" class="demo_text">
              * 30-Day Free Trial allows a maximum of five connections.
            </p>
            <!-- <SourceHubEmailsLinkAdding /> -->
          </div>
          <ClientOnly>
            <div class="intro-img-wrapper relative">
              <iframe
                v-if="!isDesktopDevice"
                id="animation1"
                title="animation1"
                src="https://anime1.sharparchive.com/"
                loading="lazy"
                @load="iframeLoaded()"
                @error="iframeError()"
                class="intro-img !w-full !h-full absolute top-0 left-0 block md:hidden"
              ></iframe>
              <iframe
                v-else
                id="animation2"
                title="animation2"
                src="https://anime2.sharparchive.com/"
                loading="lazy"
                @load="iframeLoaded()"
                @error="iframeError()"
                class="intro-img !w-full !h-full absolute top-0 left-0 hidden md:block"
              ></iframe>
            </div>
            <div id="videoWrapper" class="hero_section_video absolute-center">
              <VideoPlayer :set-size="videoOptions" />
            </div>
          </ClientOnly>
        </div>
        <div id="archive_solution" class="allInOne">
          <h2 class="landing_header_two">All-in-One Archiving Solution</h2>
          <p class="landing_p_one pt-2.5">
            Start archiving with our data and email backups in five minutes or
            less.
          </p>
          <p class="landing_p_one pt-2.5">
            Whether you are a small business, a school, a fire station, a
            government agency, or any other organization, safeguarding your data
            and communications is paramount. At Sharp Archive, we offer
            comprehensive data and communication backup services designed to
            protect your critical information and ensure seamless business
            operations. With our top-notch solutions, you can rest assured that
            your data is secure, accessible, and always available when you need
            it most. Our software programs are here to make your job easier and
            ensure your regulatory compliance, every step of the way.
          </p>
          <p class="landing_p_one pt-2.5">
            Our state-of-the-art data backup services provide robust protection
            against data loss due to system failures, cyberattacks, or natural
            disasters. We employ advanced encryption methods to secure your data
            both in transit and at rest, ensuring that sensitive information
            remains confidential and protected from unauthorized access. With
            Sharp Archive, your data is continuously backed up in real time,
            allowing for immediate recovery of the latest version in case of any
            mishap. Our scalable solutions cater to businesses of all sizes,
            providing customized backup strategies to meet your specific needs.
          </p>
        </div>
        <LandingComponent />
      </div>
    </div>
  </div>
</template>
<script setup>
import 'vue3-carousel/dist/carousel.css'
import { useBreakpoints, breakpointsTailwind } from '@vueuse/core'
import { useStore } from 'vuex'
import VideoPlayer from '~/components/VideoPlayer.vue'

// components: {
//   VideoPlayer: () =>
//     import(/* webpackPrefetch: true */ '~/components/VideoPlayer.vue'),
// },
const config = useRuntimeConfig()
const route = useRoute()
const keywords =
  'Data Backup, Communications Backup, Monitor Email, Store Email, Capture Email, Email Backups'
useSeoMeta({
  title: 'Data Backup: Communications: Monitor, Store & Capture: Email Backups',
  description: `Secure your data with Sharp Archive's top-notch data backup and email backups. Monitor, store and capture communications effortlessly.`,
})
useHead(() => ({
  link: [
    {
      hid: 'canonical',
      rel: 'canonical',
      href: `${config.public.siteUrl}/${route.name}`,
    },
  ],
  meta: [{ name: 'keywords', content: keywords }],
}))
const breakpoints1 = useBreakpoints(breakpointsTailwind)
const videoOptions = {
  autoplay: false,
  controls: true,
  sources: [
    {
      src: `/videos/sa-ui-demo.mp4`,
      type: 'video/mp4',
    },
  ],
}
const isDesktopDevice = breakpoints1.greaterOrEqual('md')
const iframeLoaded = () => {
  // clearTimeout(this.loadTimer)
}
const store = useStore()
const loggedIn = computed(() => store.state.auth.loggedIn)
</script>

<style lang="scss" scoped>
.landing_p_one {
  @apply text-lg text-center text-gray-light;
}
.landing_container {
  .main_body {
    @apply w-full h-full;
    .body {
      @apply w-full h-full;
      .intro {
        @apply w-full h-screen relative flex flex-col min-[1010px]:flex-row min-[1010px]:flex-nowrap lg:justify-between items-center xl:items-start 2xl:px-44 xl:px-26 md:px-20 px-0 2xl:pt-44 pt-64;
        background: url(~/assets/img/landing/landing_bg_img_logo1.webp);
        background-repeat: no-repeat;
        background-size: 100% 100%;
        background-color: #222831;
        border-radius: 100% / 17%;
        border-top-left-radius: 0;
        border-top-right-radius: 0;
        .intro_body {
          @apply flex flex-col lg:items-start items-center text-white text-center lg:text-left px-7 md:px-0 pt-6 md:pt-32;
          h1 {
            @apply text-3xl md:text-5xl font-medium pb-3.5;
          }
          p {
            @apply text-base md:text-xl lg:text-2xl pb-7;
          }
          .demo_text {
            @apply pt-3.5 text-lg text-gray-1100 italic;
          }
        }
        .intro-img-wrapper {
          @apply md:pt-4 lg:pt-0 2xl:pr-6;
          width: 480px;
          height: 640px;
          .intro-img {
            width: 480px;
            height: 640px;
          }
        }
        .hero_section_video {
          @apply items-center absolute-center mx-auto;
          text-align: -webkit-center;
        }
      }
      .allInOne {
        @apply text-center pt-[240px] 2xl:px-44 md:px-32 px-5;
      }
    }
  }
}
.absolute-center {
  position: absolute;
  width: 655px;
  height: 368px;
  bottom: -400px;
  left: 50%;
  transform: translate(-50%, -50%);
}
@media (min-width: 1024px) {
  .absolute-center {
    bottom: -320px;
  }
}
@media (min-width: 1200px) {
  .absolute-center {
    bottom: -360px;
  }
}
@media (max-width: 1023px) {
  .landing_container .main_body .body .intro .intro-img-wrapper {
    width: 100%;
    height: 670px;
  }
  .landing_container .main_body .body .intro .intro-img-wrapper .intro-img {
    @apply pt-4 md:pt-8;
    height: 670px;
  }
  .absolute-center {
    bottom: -310px;
  }
}
@media (max-width: 900px) {
  .absolute-center {
    bottom: -240px;
  }
}
@media (max-width: 767px) {
  .absolute-center {
    bottom: -120px;
  }
}
@media (max-width: 667px) {
  .landing_container .main_body .body .intro {
    height: 200vh;
  }
  .absolute-center {
    width: 335px;
  }
  .landing_container .main_body .body .intro .intro-img-wrapper .intro-img {
    width: 110%;
    margin: auto;
  }
  .landing_container .main_body .body .intro .intro-img-wrapper {
    width: 100%;
  }
}
@media (max-height: 1050px) and (min-height: 960px) {
  .landing_container .main_body .body .intro {
    height: 120vh;
  }
}
@media (max-height: 959px) and (min-height: 768px) {
  .landing_container .main_body .body .intro {
    height: 130vh;
  }
}
@media (max-width: 1023px) and (min-width: 768px) {
  .landing_container .main_body .body .intro {
    height: 145vh;
  }
}
@media (max-height: 767px) and (max-width: 1600px) {
  .landing_container .main_body .body .intro {
    height: 150vh;
  }
  .landing_container .main_body .body .allInOne {
    padding-top: 220px; // 140px
  }
}
@media (max-height: 767px) and (max-width: 1366px) {
  .landing_container .main_body .body .intro {
    height: 180vh;
  }
  .landing_container .main_body .body .intro .intro-img-wrapper {
    height: 590px;
  }
  .landing_container .main_body .body .intro .intro-img-wrapper .intro-img {
    height: 590px;
  }
}
@media (min-width: 1300px) and (max-width: 1440px) {
  .absolute-center {
    width: 453px;
    height: 254px;
    bottom: -244px;
  }
  .landing_container .main_body .body .intro {
    border-radius: 100% / 13%;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }
  .landing_container .main_body .body .allInOne {
    padding-top: 140px;
  }
}
@media (min-width: 768px) and (max-width: 1299px) {
  .absolute-center {
    width: 453px;
    height: 254px;
    bottom: -244px;
  }
  .landing_container .main_body .body .intro {
    border-radius: 100% / 10%;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }
  .landing_container .main_body .body .allInOne {
    padding-top: 140px;
  }
}
@media (max-width: 767px) {
  .absolute-center {
    width: 335px;
    height: 188px;
    bottom: -178px;
  }
  .landing_container .main_body .body .intro {
    border-radius: 100% / 9%;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }
  .landing_container .main_body .body .allInOne {
    padding-top: 100px;
  }
}
@media (max-width: 500px) and (min-height: 500px) and (max-height: 927px) {
  .landing_container .main_body .body .intro {
    border-radius: 100% / 5%;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    height: auto;
  }
}
@media (max-width: 375px) {
  .landing_container .main_body .body .intro .intro-img-wrapper .intro-img {
    width: 111%;
  }
}
@media (max-height: 864px) {
  @media (min-width: 1024px) {
    .landing_container .main_body .body .intro .intro-img-wrapper {
      height: 640px;
    }
    .landing_container .main_body .body .intro .intro-img-wrapper .intro-img {
      height: 640px;
    }
  }
}
</style>
