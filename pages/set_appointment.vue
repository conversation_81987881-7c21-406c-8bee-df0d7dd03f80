<script setup lang="ts">
import { useRuntimeConfig, useRoute, useSeoMeta, useHead } from '#app'
import 'vue3-carousel/dist/carousel.css'
import { useStore } from 'vuex'
import Company1 from '/images/seo/Company_1.webp'
import Company2 from '/images/seo/Company_4.webp'
import Company3 from '/images/seo/Company_3.webp'
import Company4 from '/images/seo/Company_2.webp'
import Company5 from '/images/seo/Company_5.webp'

// Define customer type
type Customer = {
  img: string // Assuming Company1, Company2, etc., are imported paths or URLs
  width: string
  height: string
}

// Define video source type
type VideoSource = {
  src: string
  type: string
}

// Define video options type
type VideoOptions = {
  autoplay: boolean
  controls: boolean
  sources: VideoSource[]
}

const config = useRuntimeConfig()
const route = useRoute()
const store = useStore()
const keywords =
  'Data Backup, Communications Backup, Monitor Email, Store Email, Capture Email, Email Backups'
useSeoMeta({
  title: 'Data Backup: Communications: Monitor, Store & Capture: Email Backups',
  description: `Secure your data with Sharp Archive's top-notch data backup and email backups. Monitor, store and capture communications effortlessly.`,
})
useHead(() => ({
  link: [
    {
      hid: 'canonical',
      rel: 'canonical',
      href: `${config.public.siteUrl}/${String(route.name)}`,
    },
  ],
  meta: [{ name: 'keywords', content: keywords }],
}))

const customers = ref<Customer[]>([
  {
    img: Company1,
    width: '128',
    hight: '128',
  },
  // {
  //   img: Company2,
  //   width: '105',
  //   hight: '121',
  // },
  {
    img: Company4,
    width: '100',
    hight: '128',
  },
  {
    img: Company3,
    width: '128',
    hight: '128',
  },
  // {
  //   img: Company5,
  //   width: '344',
  //   hight: '66',
  // },
])
const videoOptions = ref<VideoOptions>({
  autoplay: false,
  controls: true,
  sources: [
    {
      src: `${config.public.siteUrl}/videos/sa-ui-demo.mp4`,
      type: 'video/mp4',
    },
  ],
})
const show = ref<Boolean>(false)
const loggedIn = computed(() => store.state.auth.loggedIn)
const { archivingSoftware, initMap } = useReview()
const { GM_load, GM_instance } = useGoogleMaps()
onMounted(() => {
  GM_load().then(() => {
    initMap(GM_instance)
  })
  setTimeout(() => {
    show.value = true
  }, 3000)

  const img = document.createElement('img')
  img.src =
    'https://ct.capterra.com/capterra_tracker.gif?vid=2206757&vkey=5c9706701efec6c4d3032f67c8a8c486'
  // Append the image as the last child of the body
  document.body.insertBefore(img, null)
})
</script>

<template>
  <div class="landing_container">
    <div class="main_body">
      <div class="body pb-26">
        <div
          class="intro w-full !xl:max-h-[1280px] lg:max-h-[1880px] lg:min-h-screen xl:min-h-full !h-auto flex justify-center"
        >
          <div
            class="flex flex-col w-full h-full max-w-[1600px] items-center justify-between pb-[110px]"
          >
            <div
              class="flex lg:flex-row lg:space-x-10 space-x-0 lg:space-y-0 space-y-10 flex-col w-full lg:justify-between items-center justify-center"
            >
              <div
                class="flex flex-col space-y-4 text-white lg:max-w-[629px] lg:w-1/2 w-full lg:items-start items-center"
              >
                <h1
                  class="text-3xl leading-[40px] md:text-5xl md:leading-[60px] lg:text-left text-center font-medium"
                >
                  Compliance without complexity.
                </h1>
                <p
                  class="text-base md:text-xl lg:text-2xl lg:text-left text-center pb-3"
                >
                  See how Sharp Archive can help you easily monitor, capture,
                  and store your emails and communications with our data backup
                  services.
                </p>
                <div class="w-full flex lg:justify-start justify-center">
                  <NuxtLink to="/appointment" class="orange_button">
                    Book a Demo
                  </NuxtLink>
                </div>
                <p
                  v-if="!loggedIn"
                  class="text-lg text-gray-1100 italic lg:text-left text-center"
                >
                  * 30-Day Free Trial allows a maximum of five connections.
                </p>
              </div>
              <div
                class="lg:w-1/2 lg:max-w-[530px] w-full !h-auto video-player-section"
              >
                <div class="flex flex-col !space-y-3">
                  <p
                    class="font-medium text-2xl md:text-3xl lg:text-left text-center text-white"
                  >
                    Save <span class="text-[#E4801D]">30%</span> on your current
                    Bill
                  </p>
                  <p
                    class="text-base md:text-xl lg:text-2xl lg:text-left text-center text-[#C2C2C2]"
                  >
                    New Customers pay $74/month (3 social media accounts
                    included). Add more social media feeds at $7/each per month
                  </p>
                </div>
                <!-- <LazyVideoPlayer
                  v-if="show"
                  class="video-js w-full"
                  :set-size="videoOptions"
                />
                <div
                  v-else
                  class="w-full h-full bg-slate-200 animate-pulse rounded-xl"
                ></div> -->
              </div>
            </div>
            <div
              class="md:px-4 flex items-center md:justify-between justify-around md:flex-nowrap flex-wrap max-w-[899px] w-full mt-[140px]"
            >
              <div
                v-for="(item, sIndex) in archivingSoftware"
                :key="'s-image' + sIndex"
                class="md:mt-0 mt-8 flex flex-col items-center md:mx-4"
              >
                <VLazyImage
                  :src="item.image"
                  class="lg:h-17 h-10 mx-auto monitoring_img"
                  :style="{ '--width': item.width }"
                  :alt="`${item.id} img`"
                />
                <p class="text-center pt-11 flex items-center space-x-3">
                  <span class="text-white"
                    ><strong class="font-bold">{{ item.rating }}</strong></span
                  >
                  <span>
                    <ClientOnly>
                      <fa
                        :class="item.rating < 0.5 ? 'text-white' : 'rating'"
                        :icon="[
                          'fas',
                          item.rating >= 1 || item.rating < 0.5
                            ? 'star'
                            : 'star-half-alt',
                        ]"
                      />
                      <fa
                        :class="item.rating < 1.5 ? 'text-white' : 'rating'"
                        :icon="[
                          'fas',
                          item.rating >= 2 || item.rating < 1.5
                            ? 'star'
                            : 'star-half-alt',
                        ]"
                      />
                      <fa
                        :class="item.rating < 2.5 ? 'text-white' : 'rating'"
                        :icon="[
                          'fas',
                          item.rating >= 3 || item.rating < 2.5
                            ? 'star'
                            : 'star-half-alt',
                        ]"
                      />
                      <fa
                        :class="item.rating < 3.5 ? 'text-white' : 'rating'"
                        :icon="[
                          'fas',
                          item.rating >= 4 || item.rating < 3.5
                            ? 'star'
                            : 'star-half-alt',
                        ]"
                      />
                      <fa
                        :class="item.rating < 4.5 ? 'text-white' : 'rating'"
                        :icon="[
                          'fas',
                          item.rating >= 5 || item.rating < 4.5
                            ? 'star'
                            : 'star-half-alt',
                        ]"
                      />
                    </ClientOnly>
                  </span>
                </p>
              </div>
            </div>
          </div>
        </div>
        <div class="flex flex-col items-center mt-[150px]">
          <div class="flex flex-wrap justify-center">
            <div
              class="flex justify-center items-center md:mb-[30px] mb-11 flex-wrap box-section md:mx-[80px] mx-[20px]"
              v-for="(customer, index) in customers"
              :key="index"
            >
              <NuxtImg
                loading="lazy"
                format="webp"
                :width="customer.width"
                :height="customer.height"
                class=""
                :src="customer.img"
                :alt="index"
              />
            </div>
          </div>
        </div>
        <LandingComponent :bg-color="'#F1F2F6'" />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.landing_container {
  .main_body {
    @apply w-full h-full;
    .body {
      @apply w-full h-full bg-[#F1F2F6];
      .intro {
        @apply w-full h-screen relative flex flex-col min-[1010px]:flex-row min-[1010px]:flex-nowrap lg:justify-center items-center xl:items-start 2xl:px-44 xl:px-26 md:px-4 px-4 2xl:pt-44 pt-64;
        background: url(~/assets/img/landing/landing_bg_img_logo1.png);
        background-repeat: no-repeat;
        background-size: 100% 100%;
        background-color: #222831;
        border-radius: 100% / 17%;
        border-top-left-radius: 0;
        border-top-right-radius: 0;
        .intro_body {
          @apply flex flex-col lg:items-start items-center text-white text-center lg:text-left px-7 md:px-0 pt-6 md:pt-32;
          h1 {
            @apply text-3xl md:text-5xl font-medium pb-3.5;
          }
          p {
            @apply text-base md:text-xl lg:text-2xl pb-7;
          }
          .demo_text {
            @apply pt-3.5 text-lg text-gray-1100 italic;
          }
        }
      }
    }
  }
}
.box-section {
  width: auto;
  height: auto;
}
.rating {
  color: #ffbe05;
}
.monitoring_img {
  width: var(--width);
}
.video-player-section {
  width: 655px;
  height: 368px;
}
@media (max-width: 767px) {
  .video-player-section {
    width: 335px;
    height: 188px;
  }
}
@media (min-width: 1300px) and (max-width: 1440px) {
  .video-player-section {
    width: 453px;
    height: 254px;
  }
}
@media (min-width: 768px) and (max-width: 1299px) {
  .video-player-section {
    width: 453px;
    height: 254px;
  }
  .landing_container .main_body .body .intro {
    border-radius: 100% / 10%;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }
}
@media (max-width: 767px) {
  .landing_container .main_body .body .intro {
    border-radius: 100% / 9%;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }
}
@media (max-width: 500px) and (min-height: 500px) and (max-height: 927px) {
  .landing_container .main_body .body .intro {
    border-radius: 100% / 5%;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    height: auto;
  }
}
</style>
