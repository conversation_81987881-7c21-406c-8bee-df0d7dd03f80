import { mount, RouterLinkStub, shallowMount } from '@vue/test-utils'
import { mockNuxtImport } from '@nuxt/test-utils/runtime'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { createStore } from 'vuex'
import mainStore from '~/store'
import authStore from '~/store/auth'
import Landing from '~/pages/index.vue'
import { ref } from 'vue'
import { useReview } from '~/composables/useReview'
import { useGoogleMaps } from '~/composables/useGoogleMaps'
import image1 from '/images/landing/archivingSoft/image1.webp'
import image3 from '/images/landing/archivingSoft/image2.webp'
import image4 from '/images/landing/archivingSoft/image3.webp'
import DemoImage from '/images/landing/demoHome.webp'
import LandingComponent from '~/components/LandingComponent.vue'
import { Carousel, Slide } from 'vue3-carousel' // Mock these components
import { useRoute } from 'vue-router'

// Mock composables
vi.mock('@/composables/useGoogleMaps', () => ({
  useGoogleMaps: vi.fn(),
}))

// Mocking the useReview composable
vi.mock('@/composables/useReview', () => {
  return {
    useReview: vi.fn(() => ({
      archivingSoftware: ref([
        {
          id: 'google',
          image: image1,
          rating: 4.5,
          width: '80',
          sizes: 'lg:w-[80px] w-[54px]',
        },
        {
          id: 'capterra',
          image: image3,
          rating: 4.5,
          width: '358',
          sizes: 'lg:w-[358px] sm:w-[258px] w-[200px]',
        },
        {
          id: 'g2',
          image: image4,
          rating: 5,
          width: '78',
          sizes: 'lg:w-[78px] w-[54px]',
        },
      ]),
      initMap: vi.fn(), // Mock initMap if necessary
    })),
  }
})

// Mocking $fetch globally
global.$fetch = vi.fn()
// global.$route = vi.fn()

// Mock useHead using mockNuxtImport
mockNuxtImport('useHead', () => vi.fn())

// Mock useRuntimeConfig
// mockNuxtImport('useRuntimeConfig', () => () => ({
//   public: {
//     siteUrl: 'https://localhost:3000', // Example site URL
//   },
// }))

// Mock useRoute
mockNuxtImport('useRoute', () => () => ({
  name: 'index', // Example route name
}))

// Mock useLazyFetch using mockNuxtImport
mockNuxtImport('useLazyFetch', () => vi.fn())

describe('communityStore', () => {
  let store
  let wrapper
  let landingWrapper
  let GM_load
  let GM_instance
  let initMap

  // Create a fresh store for each test
  beforeEach(() => {
    // Mock the GM_load and GM_instance from the composable
    GM_load = vi.fn().mockResolvedValue()
    GM_instance = {}

    // Mock useGoogleMaps composable to return the mocked methods
    useGoogleMaps.mockReturnValue({
      GM_load,
      GM_instance,
    })

    store = createStore({
      modules: {
        auth: authStore,
      },
    })

    wrapper = shallowMount(Landing, {
      global: {
        plugins: [store],
        stubs: {
          NuxtLink: RouterLinkStub, // Use RouterLinkStub to mock NuxtLink
        },
        components: {
          LandingComponent,
        },
      },
    })
    landingWrapper = shallowMount(LandingComponent, {
      global: {
        plugins: [store],
        stubs: {
          NuxtLink: RouterLinkStub, // Use RouterLinkStub to mock NuxtLink
        },
      },
    })

    landingWrapper.vm.DemoImages = DemoImage
    // Reset the mock between tests
    vi.clearAllMocks()
  })
  afterEach(() => {
    // Clean up the mock after each test
    vi.restoreAllMocks()
  })

  const expectedResult = (targetElement, receiveContent) => {
    expect(targetElement.text()).toBe(receiveContent)
  }

  it('should render intro_body content', async () => {
    const title = wrapper.find('.intro_body h1')
    expectedResult(title, 'Compliance without complexity.')
    const description = wrapper.find('.intro_body p')
    expectedResult(
      description,
      'See how Sharp Archive can help you easily monitor, capture, and store your emails and communications with our data backup services.',
    )
  })
  it('should render Book a Demo and demo_text content if loggedIn false', async () => {
    const loggedIn = store.state.auth.loggedIn
    if (!loggedIn) {
      const nuxtLink = wrapper.findComponent('.orange_button')
      expect(nuxtLink.exists()).toBe(true)
      expect(nuxtLink.props('to')).toBe('/appointment')
      expect(nuxtLink.text()).toBe('Book a Demo')
      // Simulate a click
      await nuxtLink.trigger('click')
      // Check if the navigation target is correct (no redirect, but confirms the route)
      expect(nuxtLink.props('to')).toBe('/appointment')
      const text = wrapper.find('.intro_body .demo_text')
      expectedResult(
        text,
        '* 30-Day Free Trial allows a maximum of five connections.',
      )
    }
  })
  it('should not render Book a Demo and demo_text content if loggedIn true', async () => {
    store.commit('auth/SET_TOKEN', { loggedIn: true })
    // Ensure the component updates after the store change
    await wrapper.vm.$nextTick()
    const loggedIn = store.state.auth.loggedIn
    if (loggedIn) {
      const nuxtLink = wrapper.findComponent('.orange_button')
      expect(nuxtLink.exists()).toBe(false)
      const text = wrapper.find('.intro_body .demo_text')
      expect(text.exists()).toBe(false)
    }
  })
  it('should render allInOne content', async () => {
    const title = wrapper.find('.allInOne h2')
    expectedResult(title, 'All-in-One Archiving Solution')
    const first_description = wrapper.findAll('.allInOne p')[0]
    expectedResult(
      first_description,
      'Start archiving with our data and email backups in five minutes or less.',
    )
    const second_description = wrapper.findAll('.allInOne p')[1]
    expectedResult(
      second_description,
      'Whether you are a small business, a school, a fire station, a government agency, or any other organization, safeguarding your data and communications is paramount. At Sharp Archive, we offer comprehensive data and communication backup services designed to protect your critical information and ensure seamless business operations. With our top-notch solutions, you can rest assured that your data is secure, accessible, and always available when you need it most. Our software programs are here to make your job easier and ensure your regulatory compliance, every step of the way.',
    )
    const third_description = wrapper.findAll('.allInOne p')[2]
    expectedResult(
      third_description,
      'Our state-of-the-art data backup services provide robust protection against data loss due to system failures, cyberattacks, or natural disasters. We employ advanced encryption methods to secure your data both in transit and at rest, ensuring that sensitive information remains confidential and protected from unauthorized access. With Sharp Archive, your data is continuously backed up in real time, allowing for immediate recovery of the latest version in case of any mishap. Our scalable solutions cater to businesses of all sizes, providing customized backup strategies to meet your specific needs.',
    )
  })
  const fetchKeyFeaturesApi = async () => {
    const landingWrapper = mount(LandingComponent, {
      global: {
        plugins: [store, mainStore],
        stubs: {
          NuxtLink: RouterLinkStub, // Use RouterLinkStub to mock NuxtLink
        },
        components: {
          Carousel, // Use the actual Carousel component
          Slide, // Use the actual Slide component
        },
      },
    })

    // Check that GM_load was called
    expect(GM_load).toHaveBeenCalled()
    // Wait for the GM_load promise to resolve
    await GM_load()
    // Mock successful API response
    $fetch.mockResolvedValue({
      success: true,
      message: 'Successfully Loaded',
      data: {
        title: '<h2><b>Who</b> Are Our Data Backups For?</h2>',
        subtitle:
          'Financial Companies, Schools, Cities & Towns, Law Enforcement, Government Agencies, Politicians.',
        color: '#656565',
        features: [
          {
            title: 'Satisfy Archiving Requirements\n',
            color: '#314317',
            image:
              'https://cdn.sharparchive.com/shared/landing/satisfy-archiving-requirments-green.png',
          },
          {
            title: 'Flag Problematic Communication\n',
            color: '#474481',
            image:
              'https://cdn.sharparchive.com/shared/landing/flag-problematic-communication.png',
          },
          {
            title: 'Reduce Communication Risk\n',
            color: '#9d1616',
            image:
              'https://cdn.sharparchive.com/shared/landing/reduce-communication-risk.png',
          },
        ],
        description:
          '<p>Whether you are required to archive communication or choose to be proactive, our goal is to help you reduce your exposure to communication risk before it becomes a fine, lawsuit, or headline. We help people across multiple industries stay compliant with various regulations for communications and data backups. <br/><br/> Email communication is a critical component of modern business operations. Our communications data backup services ensure that all your emails are securely stored and easily retrievable. We support a wide range of platforms, including Microsoft 365 and Google Workspace, offering seamless integration and hassle-free setup. Our solutions enable you to capture and store every email correspondence, preserving the integrity and accessibility of your communication records. Whether you need to recover a single email or an entire mailbox, Sharp Archive makes the process quick and straightforward.</p>',
      },
    })
    mainStore.dispatch('getFeatures')
    // Trigger the component lifecycle hook manually
    await wrapper.vm.$nextTick()
    return landingWrapper // return for assertions
  }
  it('should render LandingCompoenent and fetch key-features API', async () => {
    const landingWrapper = await fetchKeyFeaturesApi()
  })
  it('should render the content of Who Are Our Data Backups For section', async () => {
    const landingWrapper = await fetchKeyFeaturesApi()
    // Access the keyFeatures directly from the store
    const keyFeatures = mainStore.state.keyFeatures
    expect(keyFeatures).not.toBeNull()
    // Find the element
    const title = landingWrapper.find('#keyFeatures .title')
    keyFeatures.title = title.html()
    expect(title.html()).toBe(keyFeatures.title)
    const subtitle = landingWrapper.find('#keyFeatures .subtitle')
    expect(subtitle.text()).toBe(keyFeatures.subtitle)
    expect(landingWrapper.findAll('#keyFeatures .feature-item').length).toBe(
      keyFeatures.features.length,
    )
    if (keyFeatures.features) {
      keyFeatures.features.forEach((feature, index) => {
        const img = landingWrapper.findComponent(
          `#keyFeatures #feature-item${index} #img${index}`,
        )
        const title = landingWrapper.find(
          `#keyFeatures #feature-item${index} #title${index}`,
        )
        expect(img.exists()).toBe(true)
        expect(img.props('src')).toBe(feature.image)
        expect(title.text()).toBe(feature.title)
      })
    }
    keyFeatures.description = landingWrapper
      .find('#keyFeatures .description')
      .html()
    expect(landingWrapper.find('#keyFeatures .description').html()).toBe(
      keyFeatures.description,
    )
  })
  it('should render the content of See how Sharp Archive will simplify your archiving section', async () => {
    const landingWrapper = await fetchKeyFeaturesApi()
    // Find the element
    const title = landingWrapper.find('#demo_section h2')
    expect(title.text()).toBe(
      'See how Sharp Archive will simplify your archiving',
    )
    const description = landingWrapper.find('#demo_section p')
    expect(description.text()).toBe(
      'Sharp Archive goes beyond just backing up your data. We provide comprehensive monitoring and storage solutions that allow you to capture, store, and analyze your emails effortlessly. Check out how our software works with our live demo!',
    )
    const img = landingWrapper.findComponent(`#demo_section .demo_image`)
    expect(img.exists()).toBe(true)
    expect(img.props('src')).toBe(landingWrapper.vm.DemoImages)
    const viewLiveDemo = landingWrapper.findComponent('#demo_section #livedemo')
    expect(viewLiveDemo.text()).toBe('View Live Demo')
    expect(viewLiveDemo.props('to')).toBe('https://demo.sharparchive.com/')
    await viewLiveDemo.trigger('click')
    expect(viewLiveDemo.props('to')).toBe('https://demo.sharparchive.com/')
    const bookDemo = landingWrapper.findComponent('#demo_section #book_demo')
    expect(bookDemo.text()).toBe('Book a Demo')
    expect(bookDemo.props('to')).toBe('/appointment')
    await bookDemo.trigger('click')
    expect(bookDemo.props('to')).toBe('/appointment')
    mainStore.commit('SET_SHOW_SUB_MENU', false)
    expect(mainStore.state.showSubMenu).toBe(false)
  })
  it('should render the content of See how Simple intuitive interface section', async () => {
    const landingWrapper = await fetchKeyFeaturesApi()
    // Find the element
    const title = landingWrapper.find('#intuitive_interface h2')
    expect(title.text()).toBe('Simple intuitive interface')
    const description = landingWrapper.find('#intuitive_interface .description')
    expect(description.text()).toBe(
      'Unlimited users at no cost. Your data, always 100% yours, no download costs. Simple transparent usage-based pricing based on number of “feeds” (accounts) connected. No long-term contracts, month-to-month only.',
    )
    // Access the newIntuitiveInterface variable from the component instance
    const newIntuitiveInterface = landingWrapper.vm.newIntuitiveInterface
    expect(
      landingWrapper.findAll('#intuitive_interface .interfaceFeature_item')
        .length,
    ).toBe(newIntuitiveInterface.interfaceFeature.length)
    if (newIntuitiveInterface.interfaceFeature) {
      newIntuitiveInterface.interfaceFeature.forEach(
        (interfaceFeature, index) => {
          const img = landingWrapper.findComponent(
            `#intuitive_interface #newIntuitiveInterface${index} #img${index}`,
          )
          const title = landingWrapper.find(
            `#intuitive_interface #newIntuitiveInterface${index} #title${index}`,
          )
          expect(img.exists()).toBe(true)
          expect(img.props('src')).toBe(interfaceFeature.image)
          expect(title.text()).toBe(interfaceFeature.title)
        },
      )
    }
  })
  it('should render the content of Are You Required to Retain? section', async () => {
    const landingWrapper = await fetchKeyFeaturesApi()
    // Find the element
    const title = landingWrapper.find('.required_retain h2')
    expect(title.text()).toBe('Are You Required to Retain?')
    const description = landingWrapper.find(
      '.required_retain .requiredRetain_des',
    )
    expect(description.text()).toBe(
      'Five Minute Set Up. Instantly Archive All Your Communications With Our Data Backup Services. Avoid Lawsuits or Fines for the Lowest Cost on the Market.',
    )
    // Access the newIntuitiveInterface variable from the component instance
    const requiredToRetains = landingWrapper.vm.requiredToRetain
    expect(
      landingWrapper.findAll('.required_retain .required_to_retain_item')
        .length,
    ).toBe(requiredToRetains.length)
    if (requiredToRetains) {
      requiredToRetains.forEach((requiredToRetain, index) => {
        const img = landingWrapper.findComponent(
          `.required_retain #requiredToRetain${index} #img${index}`,
        )
        const title = landingWrapper.find(
          `.required_retain #requiredToRetain${index} #content${index}`,
        )
        const link = landingWrapper.findComponent(
          `.required_retain #requiredToRetain${index} #link${index}`,
        )
        expect(img.exists()).toBe(true)
        expect(img.props('src')).toBe(requiredToRetain.image)
        expect(title.text()).toBe(requiredToRetain.content)
        expect(link.props('to')).toBe(requiredToRetain.link)
      })
    }
    expect(landingWrapper.find('.benifits').text()).toBe(
      `See how efficient your data backups and communications archiving can be with Sharp Archive’s beautiful interface where you can easily archive, search, and download your data. Not only will this save you time and frustration, but our simple usage-based pricing may save you thousands per year on your data backups.  Choose Sharp Archive for your data and communication backup needs and experience unparalleled security, reliability, and peace of mind. Secure your future with us today. Book your demo now!`,
    )
  })
  it('should render the content of Data Archiving Software Peer Recognition section', async () => {
    const landingWrapper = await fetchKeyFeaturesApi()
    // Find the element
    const title = landingWrapper.find('#archiving-soft h2')
    expect(title.text()).toBe('Data Archiving Software Peer Recognition')
  })
  it('renders the correct number of slides', async () => {
    const landingWrapper = await fetchKeyFeaturesApi()

    // Check that GM_load was called
    expect(GM_load).toHaveBeenCalled()
    // Wait for the GM_load promise to resolve
    await GM_load()
    // Access `archivingSoftware` using the composable within the component's context
    const { archivingSoftware } = useReview()
    await landingWrapper.vm.$nextTick()
    const slides = landingWrapper.findAllComponents(Slide)
    expect(slides.length).toBe(archivingSoftware.value.length * 3)
  })
  it('renders slides content', async () => {
    const landingWrapper = await fetchKeyFeaturesApi()

    // Check that GM_load was called
    expect(GM_load).toHaveBeenCalled()
    // Wait for the GM_load promise to resolve
    await GM_load()
    // Access `archivingSoftware` using the composable within the component's context
    const { archivingSoftware } = useReview()
    await landingWrapper.vm.$nextTick()
    const slides = landingWrapper.findAllComponents(Slide)
    slides.forEach((slide, index) => {
      const img = slide.findComponent(`#img${index}`)
      const rating = slide.find(`#rating${index}`)
      archivingSoftware.value.forEach((software, softwareIndex) => {
        if (softwareIndex === index) {
          expect(img.exists()).toBe(true)
          expect(img.props('src')).toBe(software.image)
          expect(rating.text()).toBe(`${software.rating}`)
        }
      })
    })
  })
})
